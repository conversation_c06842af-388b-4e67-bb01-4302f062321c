import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import DateTimePicker from '@react-native-community/datetimepicker';
import Icon from 'react-native-vector-icons/MaterialIcons';

import ClayCard from '../components/ClayCard';
import ClayButton from '../components/ClayButton';
import {colors} from '../theme/colors';
import {NotificationService} from '../services/NotificationService';
import {StorageService} from '../services/StorageService';

const SettingsScreen: React.FC = () => {
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [selectedTime, setSelectedTime] = useState(new Date());
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [messageType, setMessageType] = useState('motivational');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const enabled = await StorageService.getNotificationsEnabled();
      const time = await StorageService.getNotificationTime();
      const type = await StorageService.getMessageType();
      
      setNotificationsEnabled(enabled);
      setSelectedTime(new Date(time));
      setMessageType(type);
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const toggleNotifications = async (value: boolean) => {
    setNotificationsEnabled(value);
    await StorageService.setNotificationsEnabled(value);
    
    if (value) {
      await NotificationService.scheduleNotification(selectedTime, messageType);
      Alert.alert('Notifications Enabled', 'You will receive daily messages at your selected time!');
    } else {
      await NotificationService.cancelNotifications();
      Alert.alert('Notifications Disabled', 'Daily message notifications have been turned off.');
    }
  };

  const onTimeChange = async (event: any, selected?: Date) => {
    setShowTimePicker(false);
    if (selected) {
      setSelectedTime(selected);
      await StorageService.setNotificationTime(selected.toISOString());
      
      if (notificationsEnabled) {
        await NotificationService.scheduleNotification(selected, messageType);
        Alert.alert(
          'Time Updated',
          `Daily messages will now be sent at ${selected.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
          })}`
        );
      }
    }
  };

  const changeMessageType = async (type: string) => {
    setMessageType(type);
    await StorageService.setMessageType(type);
    
    if (notificationsEnabled) {
      await NotificationService.scheduleNotification(selectedTime, type);
    }
    
    Alert.alert('Message Type Updated', `You will now receive ${type} messages!`);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const messageTypes = [
    {id: 'motivational', title: 'Motivational', icon: '💪', description: 'Inspiring and uplifting messages'},
    {id: 'philosophical', title: 'Philosophical', icon: '🧠', description: 'Coming soon...', disabled: true},
    {id: 'fitness', title: 'Fitness', icon: '🏃', description: 'Coming soon...', disabled: true},
    {id: 'mindfulness', title: 'Mindfulness', icon: '🧘', description: 'Coming soon...', disabled: true},
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <Text style={styles.title}>Settings ⚙️</Text>
          <Text style={styles.subtitle}>Customize your daily message experience</Text>
        </View>

        <ClayCard style={styles.card} variant="primary">
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Icon name="notifications" size={24} color={colors.textPrimary} />
              <View style={styles.settingText}>
                <Text style={styles.settingTitle}>Daily Notifications</Text>
                <Text style={styles.settingDescription}>
                  Receive your daily message at the scheduled time
                </Text>
              </View>
            </View>
            <Switch
              value={notificationsEnabled}
              onValueChange={toggleNotifications}
              trackColor={{false: colors.textLight, true: colors.secondary}}
              thumbColor={notificationsEnabled ? colors.primary : colors.surface}
            />
          </View>
        </ClayCard>

        <ClayCard style={styles.card} variant="secondary">
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Icon name="schedule" size={24} color={colors.textPrimary} />
              <View style={styles.settingText}>
                <Text style={styles.settingTitle}>Notification Time</Text>
                <Text style={styles.settingDescription}>
                  Current time: {formatTime(selectedTime)}
                </Text>
              </View>
            </View>
          </View>
          <ClayButton
            title="Change Time"
            onPress={() => setShowTimePicker(true)}
            variant="accent"
            size="small"
            style={styles.timeButton}
          />
        </ClayCard>

        <ClayCard style={styles.card} variant="accent">
          <View style={styles.cardHeader}>
            <Icon name="category" size={24} color={colors.textPrimary} />
            <Text style={styles.cardTitle}>Message Type</Text>
          </View>
          
          <View style={styles.messageTypes}>
            {messageTypes.map((type) => (
              <ClayButton
                key={type.id}
                title={`${type.icon} ${type.title}`}
                onPress={() => !type.disabled && changeMessageType(type.id)}
                variant={messageType === type.id ? 'primary' : 'secondary'}
                disabled={type.disabled}
                style={[
                  styles.typeButton,
                  messageType === type.id && styles.selectedType,
                ]}
              />
            ))}
          </View>
        </ClayCard>

        <ClayCard style={styles.card} variant="neutral">
          <View style={styles.cardHeader}>
            <Icon name="info" size={24} color={colors.textPrimary} />
            <Text style={styles.cardTitle}>About</Text>
          </View>
          
          <Text style={styles.aboutText}>
            Daily Message App delivers personalized, AI-generated motivational content 
            to inspire and uplift you every day. More message types coming soon!
          </Text>
          
          <View style={styles.versionInfo}>
            <Text style={styles.versionText}>Version 1.0.0</Text>
            <Text style={styles.versionText}>Made with ❤️ for daily inspiration</Text>
          </View>
        </ClayCard>

        {showTimePicker && (
          <DateTimePicker
            value={selectedTime}
            mode="time"
            is24Hour={false}
            display="default"
            onChange={onTimeChange}
          />
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  card: {
    margin: 20,
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 12,
    flex: 1,
  },
  settingTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  timeButton: {
    marginTop: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginLeft: 8,
  },
  messageTypes: {
    gap: 12,
  },
  typeButton: {
    marginBottom: 8,
  },
  selectedType: {
    shadowColor: colors.primary,
    shadowOpacity: 0.3,
  },
  aboutText: {
    fontSize: 16,
    lineHeight: 24,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  versionInfo: {
    alignItems: 'center',
  },
  versionText: {
    fontSize: 12,
    color: colors.textLight,
    marginBottom: 4,
  },
});

export default SettingsScreen; 