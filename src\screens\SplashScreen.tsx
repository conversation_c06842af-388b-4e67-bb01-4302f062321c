import React, {useEffect, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import Svg, {Path, Defs, LinearGradient as SvgLinearGradient, Stop} from 'react-native-svg';

import {colors} from '../theme/colors';

interface SplashScreenProps {
  onAnimationComplete: () => void;
}

const {width, height} = Dimensions.get('window');

const SplashScreen: React.FC<SplashScreenProps> = ({onAnimationComplete}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.3)).current;
  const logoRotateAnim = useRef(new Animated.Value(0)).current;
  const textSlideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    const animationSequence = Animated.sequence([
      // Logo appears and scales up
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      // Logo gentle rotation and text slide in
      Animated.parallel([
        Animated.timing(logoRotateAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.spring(textSlideAnim, {
          toValue: 0,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
      ]),
      // Hold for a moment
      Animated.delay(1200),
      // Fade out
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]);

    animationSequence.start(() => {
      onAnimationComplete();
    });
  }, [fadeAnim, scaleAnim, logoRotateAnim, textSlideAnim, onAnimationComplete]);

  const logoRotation = logoRotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const AuraFlowLogo = () => (
    <Svg width="120" height="120" viewBox="0 0 120 120">
      <Defs>
        <SvgLinearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor="#E879F9" stopOpacity="1" />
          <Stop offset="50%" stopColor="#C084FC" stopOpacity="1" />
          <Stop offset="100%" stopColor="#8B5CF6" stopOpacity="1" />
        </SvgLinearGradient>
        <SvgLinearGradient id="logoShadow" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor="#A855F7" stopOpacity="0.6" />
          <Stop offset="100%" stopColor="#7C3AED" stopOpacity="0.8" />
        </SvgLinearGradient>
      </Defs>
      
      {/* Shadow layer */}
      <Path
        d="M25 45 Q35 25, 55 35 Q75 45, 85 25 Q95 35, 105 45 Q95 65, 75 55 Q55 65, 45 85 Q35 75, 25 65 Q35 55, 25 45 Z"
        fill="url(#logoShadow)"
        transform="translate(2, 4)"
        opacity="0.4"
      />
      
      {/* Main logo */}
      <Path
        d="M25 45 Q35 25, 55 35 Q75 45, 85 25 Q95 35, 105 45 Q95 65, 75 55 Q55 65, 45 85 Q35 75, 25 65 Q35 55, 25 45 Z"
        fill="url(#logoGradient)"
      />
      
      {/* Highlight */}
      <Path
        d="M30 40 Q40 30, 50 35 Q60 40, 70 30 Q75 35, 80 40"
        fill="none"
        stroke="rgba(255,255,255,0.6)"
        strokeWidth="2"
        strokeLinecap="round"
      />
    </Svg>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <LinearGradient
        colors={['#8B5CF6', '#A855F7', '#C084FC', '#E879F9']}
        style={styles.gradient}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 1}}>
        
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{scale: scaleAnim}],
            },
          ]}>
          
          {/* Logo Container */}
          <Animated.View
            style={[
              styles.logoContainer,
              {
                transform: [
                  {rotate: logoRotation},
                  {scale: scaleAnim},
                ],
              },
            ]}>
            <View style={styles.logoShadow}>
              <AuraFlowLogo />
            </View>
          </Animated.View>

          {/* App Name */}
          <Animated.View
            style={[
              styles.textContainer,
              {
                transform: [{translateY: textSlideAnim}],
                opacity: fadeAnim,
              },
            ]}>
            <Text style={styles.appName}>AuraFlow</Text>
            <Text style={styles.tagline}>Daily Inspiration & Motivation</Text>
          </Animated.View>

          {/* Decorative Elements */}
          <View style={styles.decorativeElements}>
            <View style={[styles.floatingElement, styles.element1]} />
            <View style={[styles.floatingElement, styles.element2]} />
            <View style={[styles.floatingElement, styles.element3]} />
          </View>
        </Animated.View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    marginBottom: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoShadow: {
    shadowColor: '#7C3AED',
    shadowOffset: {
      width: 0,
      height: 20,
    },
    shadowOpacity: 0.4,
    shadowRadius: 25,
    elevation: 20,
  },
  textContainer: {
    alignItems: 'center',
  },
  appName: {
    fontSize: 42,
    fontWeight: '800',
    color: '#FFFFFF',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: {width: 0, height: 2},
    textShadowRadius: 8,
    letterSpacing: 1,
  },
  tagline: {
    fontSize: 16,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 4,
  },
  decorativeElements: {
    position: 'absolute',
    width: width,
    height: height,
    pointerEvents: 'none',
  },
  floatingElement: {
    position: 'absolute',
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  element1: {
    width: 80,
    height: 80,
    top: height * 0.15,
    left: width * 0.1,
  },
  element2: {
    width: 60,
    height: 60,
    top: height * 0.75,
    right: width * 0.15,
  },
  element3: {
    width: 40,
    height: 40,
    top: height * 0.25,
    right: width * 0.25,
  },
});

export default SplashScreen;
