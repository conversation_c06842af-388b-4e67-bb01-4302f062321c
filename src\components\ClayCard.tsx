import React from 'react';
import {View, StyleSheet, ViewStyle} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors} from '../theme/colors';

interface ClayCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'primary' | 'secondary' | 'accent' | 'neutral';
}

const ClayCard: React.FC<ClayCardProps> = ({
  children,
  style,
  variant = 'neutral',
}) => {
  const getGradientColors = () => {
    switch (variant) {
      case 'primary':
        return [colors.primary, colors.surface];
      case 'secondary':
        return [colors.secondary, colors.surface];
      case 'accent':
        return [colors.accent, colors.surface];
      default:
        return [colors.surface, colors.background];
    }
  };

  return (
    <View style={[styles.container, style]}>
      <LinearGradient
        colors={getGradientColors()}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 1}}
        style={styles.gradient}>
        <View style={styles.innerShadow}>{children}</View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    shadowColor: colors.shadow,
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  gradient: {
    borderRadius: 20,
    padding: 20,
  },
  innerShadow: {
    borderRadius: 16,
    shadowColor: colors.lightShadow,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.6,
    shadowRadius: 8,
  },
});

export default ClayCard; 