import { GoogleGenerativeAI } from '@google/generative-ai';

interface AIResponse {
  success: boolean;
  message?: string;
  error?: string;
}

export class AIService {
  // Store API key securely - In production, use environment variables or secure storage
  private static readonly API_KEY = 'AIzaSyD2BOV8SPuOAX2SjBUWfSjgHW4Evl4Vr-8';
  private static genAI: GoogleGenerativeAI;
  
  // Rate limiting - prevent too many API calls
  private static lastCallTime = 0;
  private static readonly MIN_CALL_INTERVAL = 1000; // 1 second between calls
  
  // Initialize the Google AI client
  private static initializeAI() {
    if (!this.genAI) {
      this.genAI = new GoogleGenerativeAI(this.API_KEY);
    }
    return this.genAI;
  }
  
  /**
   * Generate an AI-powered motivational message using Google Gemini
   * @param prompt The category-specific prompt for the AI
   * @returns Promise<AIResponse> containing success status and message/error
   */
  static async generateMessage(prompt: string): Promise<AIResponse> {
    try {
      // Rate limiting check
      const now = Date.now();
      if (now - this.lastCallTime < this.MIN_CALL_INTERVAL) {
        await this.sleep(this.MIN_CALL_INTERVAL - (now - this.lastCallTime));
      }
      this.lastCallTime = Date.now();

      // Initialize AI client
      const genAI = this.initializeAI();
      
      // Get the gemini-2.0-flash-exp model
      const model = genAI.getGenerativeModel({ 
        model: "gemini-2.0-flash-exp",
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 100,
        }
      });

      // Generate content
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      if (!text || text.trim().length === 0) {
        throw new Error('No content generated by AI');
      }

      // Clean up the response (remove quotes if present)
      const cleanedText = text.trim().replace(/^["']|["']$/g, '');
      
      return {
        success: true,
        message: cleanedText
      };

    } catch (error) {
      console.error('AIService Error:', error);
      
      // Return error details for debugging
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Test the AI API connection
   * @returns Promise<boolean> indicating if the API is working
   */
  static async testConnection(): Promise<boolean> {
    try {
      const testPrompt = "Generate a simple motivational quote in under 10 words.";
      const result = await this.generateMessage(testPrompt);
      return result.success;
    } catch (error) {
      console.error('AI API connection test failed:', error);
      return false;
    }
  }

  /**
   * Get API usage statistics (placeholder for future implementation)
   * @returns Object with usage stats
   */
  static getUsageStats() {
    return {
      totalCalls: 0, // TODO: Implement call tracking
      lastCallTime: this.lastCallTime,
      status: 'operational'
    };
  }

  /**
   * Helper method to add delays
   * @param ms Milliseconds to sleep
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate if a prompt is appropriate for the AI
   * @param prompt The prompt to validate
   * @returns boolean indicating if prompt is valid
   */
  static validatePrompt(prompt: string): boolean {
    if (!prompt || prompt.trim().length === 0) {
      return false;
    }
    
    if (prompt.length > 500) {
      return false; // Too long
    }
    
    // Add any other validation rules here
    return true;
  }
} 