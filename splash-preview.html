<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌊 AuraFlow - Splash Screen Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 25%, #C084FC 75%, #E879F9 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .splash-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            z-index: 10;
        }

        .logo-container {
            margin-bottom: 40px;
            position: relative;
            animation: logoEntrance 2s ease-out forwards;
        }

        .logo {
            width: 120px;
            height: 120px;
            filter: drop-shadow(0 20px 25px rgba(124, 58, 237, 0.4));
            animation: logoFloat 3s ease-in-out infinite;
        }

        .app-name {
            font-size: 42px;
            font-weight: 800;
            color: #FFFFFF;
            margin-bottom: 8px;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            letter-spacing: 1px;
            animation: textSlideUp 1.5s ease-out 0.5s both;
        }

        .tagline {
            font-size: 16px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
            animation: textSlideUp 1.5s ease-out 0.8s both;
        }

        .floating-elements {
            position: absolute;
            width: 100vw;
            height: 100vh;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-element {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .element-1 {
            width: 80px;
            height: 80px;
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .element-2 {
            width: 60px;
            height: 60px;
            top: 75%;
            right: 15%;
            animation-delay: 2s;
        }

        .element-3 {
            width: 40px;
            height: 40px;
            top: 25%;
            right: 25%;
            animation-delay: 4s;
        }

        .element-4 {
            width: 100px;
            height: 100px;
            bottom: 20%;
            left: 20%;
            animation-delay: 1s;
        }

        .element-5 {
            width: 30px;
            height: 30px;
            top: 60%;
            left: 80%;
            animation-delay: 3s;
        }

        @keyframes logoEntrance {
            0% {
                opacity: 0;
                transform: scale(0.3) rotate(-180deg);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.1) rotate(0deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        @keyframes logoFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-10px) rotate(5deg);
            }
        }

        @keyframes textSlideUp {
            0% {
                opacity: 0;
                transform: translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.1;
            }
            33% {
                transform: translateY(-20px) translateX(10px) scale(1.1);
                opacity: 0.2;
            }
            66% {
                transform: translateY(10px) translateX(-5px) scale(0.9);
                opacity: 0.15;
            }
        }

        .demo-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            z-index: 100;
        }

        .demo-button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .demo-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .app-name {
                font-size: 32px;
            }
            
            .tagline {
                font-size: 14px;
            }
            
            .logo {
                width: 100px;
                height: 100px;
            }
            
            .demo-controls {
                top: 10px;
                right: 10px;
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-element element-1"></div>
        <div class="floating-element element-2"></div>
        <div class="floating-element element-3"></div>
        <div class="floating-element element-4"></div>
        <div class="floating-element element-5"></div>
    </div>

    <div class="splash-container">
        <div class="logo-container">
            <svg class="logo" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#E879F9;stop-opacity:1" />
                        <stop offset="50%" style="stop-color:#C084FC;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="logoShadow" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#A855F7;stop-opacity:0.6" />
                        <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:0.8" />
                    </linearGradient>
                </defs>
                
                <!-- Shadow layer -->
                <path d="M25 45 Q35 25, 55 35 Q75 45, 85 25 Q95 35, 105 45 Q95 65, 75 55 Q55 65, 45 85 Q35 75, 25 65 Q35 55, 25 45 Z" 
                      fill="url(#logoShadow)" 
                      transform="translate(2, 4)" 
                      opacity="0.4"/>
                
                <!-- Main logo -->
                <path d="M25 45 Q35 25, 55 35 Q75 45, 85 25 Q95 35, 105 45 Q95 65, 75 55 Q55 65, 45 85 Q35 75, 25 65 Q35 55, 25 45 Z" 
                      fill="url(#logoGradient)"/>
                
                <!-- Highlight -->
                <path d="M30 40 Q40 30, 50 35 Q60 40, 70 30 Q75 35, 80 40" 
                      fill="none" 
                      stroke="rgba(255,255,255,0.6)" 
                      stroke-width="2" 
                      stroke-linecap="round"/>
            </svg>
        </div>

        <h1 class="app-name">AuraFlow</h1>
        <p class="tagline">Daily Inspiration & Motivation</p>
    </div>

    <div class="demo-controls">
        <button class="demo-button" onclick="restartAnimation()">🔄 Restart</button>
        <button class="demo-button" onclick="showMainApp()">📱 Main App</button>
        <button class="demo-button" onclick="toggleFloating()">✨ Toggle Effects</button>
    </div>

    <script>
        let animationsEnabled = true;

        function restartAnimation() {
            const logo = document.querySelector('.logo-container');
            const appName = document.querySelector('.app-name');
            const tagline = document.querySelector('.tagline');
            
            // Reset animations
            logo.style.animation = 'none';
            appName.style.animation = 'none';
            tagline.style.animation = 'none';
            
            // Force reflow
            logo.offsetHeight;
            
            // Restart animations
            logo.style.animation = 'logoEntrance 2s ease-out forwards';
            appName.style.animation = 'textSlideUp 1.5s ease-out 0.5s both';
            tagline.style.animation = 'textSlideUp 1.5s ease-out 0.8s both';
        }

        function showMainApp() {
            window.open('app-preview.html', '_blank');
        }

        function toggleFloating() {
            const elements = document.querySelectorAll('.floating-element');
            animationsEnabled = !animationsEnabled;
            
            elements.forEach(element => {
                if (animationsEnabled) {
                    element.style.animationPlayState = 'running';
                } else {
                    element.style.animationPlayState = 'paused';
                }
            });
        }

        // Auto-transition to main app after 4 seconds (commented out for demo)
        // setTimeout(() => {
        //     showMainApp();
        // }, 4000);
    </script>
</body>
</html>
