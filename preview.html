<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Message App - Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #F8FAFC 0%, #E8D5FF 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            max-width: 375px;
            margin: 0 auto;
            background: #F8FAFC;
            border-radius: 30px;
            padding: 20px;
            box-shadow: 
                0 20px 40px rgba(0,0,0,0.1),
                inset 0 2px 16px rgba(255,255,255,0.6);
            min-height: 700px;
            position: relative;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            font-size: 14px;
            font-weight: 600;
            color: #334155;
        }

        .header {
            text-align: center;
            padding: 20px 0;
        }

        .greeting {
            font-size: 28px;
            font-weight: 700;
            color: #334155;
            margin-bottom: 8px;
        }

        .date {
            font-size: 16px;
            color: #64748B;
            font-weight: 500;
        }

        .clay-card {
            background: linear-gradient(135deg, #E8D5FF 0%, #FFFFFF 100%);
            border-radius: 20px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 
                0 8px 32px rgba(0,0,0,0.1),
                inset 0 2px 16px rgba(255,255,255,0.6);
            transition: all 0.3s ease;
        }

        .clay-card:hover {
            transform: translateY(-2px);
            box-shadow: 
                0 12px 40px rgba(0,0,0,0.15),
                inset 0 2px 16px rgba(255,255,255,0.7);
        }

        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .message-type {
            font-size: 18px;
            font-weight: 600;
            color: #334155;
            margin-left: 8px;
        }

        .daily-message {
            font-size: 20px;
            line-height: 1.6;
            color: #334155;
            font-weight: 500;
            text-align: center;
            animation: fadeIn 1s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin: 20px 0;
        }

        .clay-button {
            flex: 1;
            background: linear-gradient(135deg, #C7F2D0 0%, #BFDBFE 100%);
            border: none;
            border-radius: 20px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            color: #334155;
            cursor: pointer;
            box-shadow: 
                0 8px 16px rgba(0,0,0,0.15),
                inset 0 2px 8px rgba(255,255,255,0.6);
            transition: all 0.2s ease;
        }

        .clay-button:hover {
            transform: translateY(-1px);
            box-shadow: 
                0 12px 24px rgba(0,0,0,0.2),
                inset 0 2px 8px rgba(255,255,255,0.8);
        }

        .clay-button:active {
            transform: scale(0.95);
        }

        .stats-card {
            background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFC 100%);
        }

        .stats-row {
            display: flex;
            justify-content: space-around;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #334155;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #64748B;
            font-weight: 500;
        }

        .tab-bar {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: #FFFFFF;
            border-radius: 20px;
            padding: 8px;
            display: flex;
            justify-content: space-around;
            box-shadow: 
                0 8px 32px rgba(0,0,0,0.1),
                inset 0 2px 16px rgba(255,255,255,0.6);
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 16px;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tab-item.active {
            background: linear-gradient(135deg, #E8D5FF 0%, #C7F2D0 100%);
            box-shadow: inset 0 2px 8px rgba(0,0,0,0.1);
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .tab-label {
            font-size: 12px;
            font-weight: 600;
            color: #334155;
        }

        .demo-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .demo-button {
            background: linear-gradient(135deg, #E8D5FF 0%, #C7F2D0 100%);
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            margin: 4px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .demo-button:hover {
            transform: translateY(-1px);
        }

        .notification-preview {
            position: fixed;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            background: #FFFFFF;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            max-width: 350px;
            display: none;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-20px); opacity: 0; }
            to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }

        .notification-preview.show {
            display: block;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #E8D5FF 0%, #C7F2D0 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            float: left;
            margin-right: 12px;
            font-size: 20px;
        }

        .notification-content h4 {
            font-size: 16px;
            font-weight: 600;
            color: #334155;
            margin-bottom: 4px;
        }

        .notification-content p {
            font-size: 14px;
            color: #64748B;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="demo-controls">
        <h3 style="margin-bottom: 12px; color: #334155;">🎨 Demo Controls</h3>
        <button class="demo-button" onclick="generateNewMessage()">✨ New Message</button>
        <button class="demo-button" onclick="showNotification()">🔔 Test Notification</button>
        <button class="demo-button" onclick="switchTab()">⚙️ Settings View</button>
    </div>

    <div class="notification-preview" id="notification">
        <div class="notification-icon">✨</div>
        <div class="notification-content">
            <h4>Your Daily Message</h4>
            <p id="notificationText">Your daily dose of inspiration is ready!</p>
        </div>
    </div>

    <div class="phone-container">
        <div class="status-bar">
            <span>9:41</span>
            <span>📶 🔋 100%</span>
        </div>

        <div class="header">
            <div class="greeting">Good day! 🌅</div>
            <div class="date" id="currentDate"></div>
        </div>

        <div class="clay-card">
            <div class="message-header">
                <span class="tab-icon">✨</span>
                <div class="message-type">Motivational Message</div>
            </div>
            <div class="daily-message" id="dailyMessage">
                Today is a new chapter in your story. Make it an inspiring one! 📖✨
            </div>
        </div>

        <div class="action-buttons">
            <button class="clay-button" onclick="generateNewMessage()">
                ✨ New Message
            </button>
            <button class="clay-button" onclick="refreshMessage()">
                🔄 Refresh
            </button>
        </div>

        <div class="clay-card stats-card">
            <div class="stats-row">
                <div class="stat-item">
                    <div class="stat-number">1</div>
                    <div class="stat-label">Today's Message</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">✨</div>
                    <div class="stat-label">AI Generated</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">🎯</div>
                    <div class="stat-label">Personalized</div>
                </div>
            </div>
        </div>

        <div class="tab-bar">
            <div class="tab-item active">
                <div class="tab-icon">🏠</div>
                <div class="tab-label">Home</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon">⚙️</div>
                <div class="tab-label">Settings</div>
            </div>
        </div>
    </div>

    <script>
        const messages = [
            "Today is a new chapter in your story. Make it an inspiring one! 📖✨",
            "Your potential is limitless. Believe in yourself and watch magic happen! 🌟💫",
            "Every small step forward is progress. Keep moving toward your dreams! 👣🎯",
            "You have the power to create positive change in your life today! ⚡💪",
            "Challenges are just opportunities wearing disguises. Embrace them! 🎭🚀",
            "Your mindset shapes your reality. Choose thoughts that empower you! 🧠💎",
            "Success is not final, failure is not fatal. What matters is the courage to continue! 🦁❤️",
            "You are stronger than your excuses and more powerful than your fears! 💪🔥",
            "Today's struggles are tomorrow's strengths. Keep pushing forward! 🏋️‍♀️🌈",
            "Your dreams don't have an expiration date. It's never too late to pursue them! ⏰🌟",
            "Be yourself unapologetically. The world needs your unique gifts! 🎁🌍",
            "Progress, not perfection, is the goal. Celebrate your journey! 🎉📈",
            "You are the architect of your own destiny. Build something beautiful! 🏗️🏛️",
            "Every expert was once a beginner. Your time to shine is coming! 🌅⭐",
            "Kindness is a strength, not a weakness. Spread it generously today! 💝🤲"
        ];

        let currentMessageIndex = 0;

        function generateNewMessage() {
            const messageElement = document.getElementById('dailyMessage');
            currentMessageIndex = (currentMessageIndex + 1) % messages.length;
            
            // Fade out
            messageElement.style.opacity = '0.3';
            messageElement.style.transform = 'translateY(10px)';
            
            setTimeout(() => {
                messageElement.textContent = messages[currentMessageIndex];
                messageElement.style.opacity = '1';
                messageElement.style.transform = 'translateY(0)';
            }, 300);
        }

        function refreshMessage() {
            const messageElement = document.getElementById('dailyMessage');
            messageElement.style.animation = 'none';
            setTimeout(() => {
                messageElement.style.animation = 'fadeIn 1s ease-in';
            }, 10);
        }

        function showNotification() {
            const notification = document.getElementById('notification');
            const notificationText = document.getElementById('notificationText');
            
            notificationText.textContent = messages[Math.floor(Math.random() * messages.length)];
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 4000);
        }

        function switchTab() {
            alert('🚀 In the real app, this would switch to the Settings screen with:\n\n⚙️ Notification time picker\n🎨 Message type selection\n🔔 Notification toggle\n📊 Usage statistics\n\nAll with the same beautiful claymorphism design!');
        }

        // Set current date
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Add some interactive effects
        document.querySelectorAll('.clay-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html> 