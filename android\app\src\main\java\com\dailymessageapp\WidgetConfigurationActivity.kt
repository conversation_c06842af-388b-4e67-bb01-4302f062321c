package com.dailymessageapp

import android.app.Activity
import android.appwidget.AppWidgetManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ArrayAdapter
import android.widget.EditText
import android.widget.Spinner

/**
 * The configuration screen for the {@link WidgetProvider WidgetProvider}
 */
class WidgetConfigurationActivity : Activity() {
    private var appWidgetId = AppWidgetManager.INVALID_APPWIDGET_ID
    private lateinit var categorySpinner: Spinner
    private lateinit var layoutSpinner: Spinner
    
    private val categories = arrayOf("motivational", "fitness", "career", "relationships", "confidence", "mindfulness", "goals")
    private val categoryNames = arrayOf("Motivational 🌟", "Fitness 💪", "Career 🏢", "Relationships ❤️", "Confidence ✨", "Mindfulness 🧘", "Goals 🎯")
    private val layouts = arrayOf("default", "1x1", "2x1", "4x2")
    private val layoutNames = arrayOf("Default", "Small (1x1)", "Medium (2x1)", "Large (4x2)")
    
    private var onClickListener = View.OnClickListener {
        val context = this@WidgetConfigurationActivity

        // Save selected category and layout
        val selectedCategory = categories[categorySpinner.selectedItemPosition]
        val selectedLayout = layouts[layoutSpinner.selectedItemPosition]
        
        saveCategoryPref(context, appWidgetId, selectedCategory)
        saveLayoutPref(context, appWidgetId, selectedLayout)

        // Update the app widget
        val appWidgetManager = AppWidgetManager.getInstance(context)
        updateAppWidget(context, appWidgetManager, appWidgetId)

        // Return the widget ID
        val resultValue = Intent()
        resultValue.putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
        setResult(RESULT_OK, resultValue)
        finish()
    }

    public override fun onCreate(icicle: Bundle?) {
        super.onCreate(icicle)

        // Set the result to CANCELED.  This will cause the widget host to cancel
        // out of the widget placement if the user presses the back button.
        setResult(RESULT_CANCELED)

        setContentView(R.layout.widget_configure)
        
        // Initialize spinners
        categorySpinner = findViewById(R.id.category_spinner)
        layoutSpinner = findViewById(R.id.layout_spinner)
        
        // Set up category spinner
        val categoryAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, categoryNames)
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        categorySpinner.adapter = categoryAdapter
        
        // Set up layout spinner
        val layoutAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, layoutNames)
        layoutAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        layoutSpinner.adapter = layoutAdapter
        
        findViewById<View>(R.id.add_button).setOnClickListener(onClickListener)

        // Find the widget id from the intent.
        val intent = intent
        val extras = intent.extras
        if (extras != null) {
            appWidgetId = extras.getInt(
                AppWidgetManager.EXTRA_APPWIDGET_ID, AppWidgetManager.INVALID_APPWIDGET_ID
            )
        }

        // If this activity was started with an intent without an app widget ID, finish with an error.
        if (appWidgetId == AppWidgetManager.INVALID_APPWIDGET_ID) {
            finish()
            return
        }

        // Load saved preferences
        val savedCategory = loadCategoryPref(this, appWidgetId)
        val savedLayout = loadLayoutPref(this, appWidgetId)
        
        // Set spinner selections
        categorySpinner.setSelection(categories.indexOf(savedCategory))
        layoutSpinner.setSelection(layouts.indexOf(savedLayout))
    }

    companion object {

        private const val PREFS_NAME = "com.dailymessageapp.WidgetProvider"
        private const val PREF_CATEGORY_PREFIX = "category_"
        private const val PREF_LAYOUT_PREFIX = "layout_"

        // Category preference methods
        internal fun saveCategoryPref(context: Context, appWidgetId: Int, category: String) {
            val prefs = context.getSharedPreferences(PREFS_NAME, 0).edit()
            prefs.putString(PREF_CATEGORY_PREFIX + appWidgetId, category)
            prefs.apply()
        }

        internal fun loadCategoryPref(context: Context, appWidgetId: Int): String {
            val prefs = context.getSharedPreferences(PREFS_NAME, 0)
            return prefs.getString(PREF_CATEGORY_PREFIX + appWidgetId, "motivational") ?: "motivational"
        }

        // Layout preference methods
        internal fun saveLayoutPref(context: Context, appWidgetId: Int, layout: String) {
            val prefs = context.getSharedPreferences(PREFS_NAME, 0).edit()
            prefs.putString(PREF_LAYOUT_PREFIX + appWidgetId, layout)
            prefs.apply()
        }

        internal fun loadLayoutPref(context: Context, appWidgetId: Int): String {
            val prefs = context.getSharedPreferences(PREFS_NAME, 0)
            return prefs.getString(PREF_LAYOUT_PREFIX + appWidgetId, "default") ?: "default"
        }

        // Clean up preferences when widget is deleted
        internal fun deleteWidgetPrefs(context: Context, appWidgetId: Int) {
            val prefs = context.getSharedPreferences(PREFS_NAME, 0).edit()
            prefs.remove(PREF_CATEGORY_PREFIX + appWidgetId)
            prefs.remove(PREF_LAYOUT_PREFIX + appWidgetId)
            prefs.apply()
        }
    }
}
