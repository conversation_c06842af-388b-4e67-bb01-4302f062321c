import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Animated,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors} from '../theme/colors';

interface ClayButtonProps {
  title: string;
  onPress: () => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
  variant?: 'primary' | 'secondary' | 'accent';
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
}

const ClayButton: React.FC<ClayButtonProps> = ({
  title,
  onPress,
  style,
  textStyle,
  variant = 'primary',
  disabled = false,
  size = 'medium',
}) => {
  const scaleAnim = new Animated.Value(1);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const getGradientColors = () => {
    switch (variant) {
      case 'primary':
        return [colors.primary, colors.gradientMiddle];
      case 'secondary':
        return [colors.secondary, colors.accent];
      case 'accent':
        return [colors.accent, colors.primary];
      default:
        return [colors.primary, colors.gradientMiddle];
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {padding: 12, fontSize: 14};
      case 'large':
        return {padding: 20, fontSize: 18};
      default:
        return {padding: 16, fontSize: 16};
    }
  };

  const sizeStyles = getSizeStyles();

  return (
    <Animated.View style={[{transform: [{scale: scaleAnim}]}]}>
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        activeOpacity={0.8}
        style={[styles.button, style, {opacity: disabled ? 0.5 : 1}]}>
        <LinearGradient
          colors={getGradientColors()}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}
          style={[styles.gradient, {padding: sizeStyles.padding}]}>
          <Text
            style={[
              styles.text,
              {fontSize: sizeStyles.fontSize},
              textStyle,
            ]}>
            {title}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 20,
    shadowColor: colors.shadow,
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  gradient: {
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.lightShadow,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.6,
    shadowRadius: 8,
  },
  text: {
    color: colors.textPrimary,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default ClayButton; 