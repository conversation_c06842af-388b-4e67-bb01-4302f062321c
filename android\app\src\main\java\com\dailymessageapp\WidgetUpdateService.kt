package com.dailymessageapp

import android.app.AlarmManager
import android.app.PendingIntent
import android.app.Service
import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.os.SystemClock
import android.widget.RemoteViews
import androidx.core.content.ContextCompat

class WidgetUpdateService : Service() {

    companion object {
        const val ACTION_AUTO_UPDATE = "com.dailymessageapp.AUTO_UPDATE"
        // IMPORTANT: These intervals are for WIDGET DISPLAY refresh only, NOT API calls!
        // Widget shows the SAME daily stored message, just refreshes the display
        const val UPDATE_INTERVAL_15_MIN = 15 * 60 * 1000L // 15 minutes - display refresh
        const val UPDATE_INTERVAL_1_HOUR = 60 * 60 * 1000L // 1 hour - display refresh
        const val UPDATE_INTERVAL_4_HOURS = 4 * 60 * 60 * 1000L // 4 hours - display refresh
        
        private const val PREFS_NAME = "widget_update_prefs"
        private const val PREF_UPDATE_INTERVAL = "update_interval"
        
        fun scheduleUpdates(context: Context, interval: Long) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val intent = Intent(context, WidgetUpdateService::class.java).apply {
                action = ACTION_AUTO_UPDATE
            }
            val pendingIntent = PendingIntent.getService(
                context, 0, intent, 
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            // Cancel previous alarms
            alarmManager.cancel(pendingIntent)
            
            // Schedule repeating alarm for DISPLAY refresh (NO API calls)
            alarmManager.setRepeating(
                AlarmManager.ELAPSED_REALTIME,
                SystemClock.elapsedRealtime() + interval,
                interval,
                pendingIntent
            )
            
            // Save interval preference
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().putLong(PREF_UPDATE_INTERVAL, interval).apply()
        }
        
        fun getUpdateInterval(context: Context): Long {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            return prefs.getLong(PREF_UPDATE_INTERVAL, UPDATE_INTERVAL_1_HOUR)
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_AUTO_UPDATE -> {
                updateAllWidgets()
            }
            // NO MANUAL REFRESH SUPPORT - PREVENTS COST EXPLOSION
        }
        return START_NOT_STICKY
    }
    
    private fun updateAllWidgets() {
        val appWidgetManager = AppWidgetManager.getInstance(this)
        val componentName = ComponentName(this, WidgetProvider::class.java)
        val appWidgetIds = appWidgetManager.getAppWidgetIds(componentName)
        updateWidgets(appWidgetIds)
    }
    
    private fun updateWidgets(appWidgetIds: IntArray) {
        val appWidgetManager = AppWidgetManager.getInstance(this)
        
        for (appWidgetId in appWidgetIds) {
            try {
                // Get widget configuration
                val category = WidgetConfigurationActivity.loadCategoryPref(this, appWidgetId)
                val layoutType = WidgetConfigurationActivity.loadLayoutPref(this, appWidgetId)
                
                // Get STORED daily message for this category (NO API CALL!)
                val message = getDailyStoredMessage(category)
                
                // Select appropriate layout
                val layoutRes = when (layoutType) {
                    "1x1" -> R.layout.widget_layout_1x1
                    "2x1" -> R.layout.widget_layout_2x1
                    "4x2" -> R.layout.widget_layout_4x2
                    else -> R.layout.widget_layout
                }
                
                val remoteViews = RemoteViews(packageName, layoutRes)
                remoteViews.setTextViewText(R.id.appwidget_text, message)
                
                // Set up click to open main app (NO REFRESH - NO API CALLS)
                val openAppIntent = Intent(this, MainActivity::class.java)
                val openAppPendingIntent = PendingIntent.getActivity(
                    this, appWidgetId, openAppIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
                remoteViews.setOnClickPendingIntent(R.id.widget_container, openAppPendingIntent)
                
                appWidgetManager.updateAppWidget(appWidgetId, remoteViews)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun getDailyStoredMessage(category: String): String {
        // Get today's stored message from SharedPreferences (NO API CALL!)
        val today = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault()).format(java.util.Date())
        val prefs = getSharedPreferences("daily_messages", Context.MODE_PRIVATE)
        val messageKey = "${category}_$today"
        
        // Return stored message or fallback if not found
        return prefs.getString(messageKey, null) ?: getFallbackMessage(category)
    }
    
    private fun getFallbackMessage(category: String): String {
        // Static fallback messages (NO API COST)
        return when (category) {
            "fitness" -> "💪 Your strength grows with every challenge you face!"
            "career" -> "🏢 Success is built one determined step at a time!"
            "relationships" -> "❤️ Love and kindness create the strongest connections!"
            "confidence" -> "✨ You have everything within you to shine bright!"
            "mindfulness" -> "🧘 Find peace in this moment, right here, right now!"
            "goals" -> "🎯 Every dream begins with the courage to start!"
            else -> "🌟 Today is full of possibilities waiting for you!"
        }
    }

    override fun onBind(intent: Intent): IBinder? {
        return null
    }
}

