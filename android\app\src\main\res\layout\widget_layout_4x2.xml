<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:padding="@dimen/widget_margin">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:background="@drawable/widget_card_background"
        android:orientation="vertical"
        android:padding="20dp"
        android:gravity="center">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginRight="8dp"
                android:src="@drawable/auraflow_icon"
                android:contentDescription="AuraFlow Logo" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="AuraFlow"
                android:textColor="@color/widget_brand_color"
                android:textSize="16sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif" />

        </LinearLayout>

        <TextView
            android:id="@+id/appwidget_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/appwidget_text"
            android:textColor="@color/widget_text_primary"
            android:textSize="16sp"
            android:textStyle="normal"
            android:lineSpacingMultiplier="1.4"
            android:gravity="center"
            android:maxLines="4"
            android:ellipsize="end"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Daily Inspiration & Motivation"
            android:textColor="@color/widget_text_secondary"
            android:textSize="11sp"
            android:fontFamily="sans-serif" />

    </LinearLayout>

</RelativeLayout>
