import {StorageService, MessageHistoryItem} from './StorageService';
import {AIService} from './AIService';

export class MessageService {
  // Icon mapping for different message types
  private static readonly MESSAGE_ICONS = {
    motivational: '⭐',
    philosophical: '🔮',
    success: '🎯',
    happiness: '😊',
    wisdom: '🦉',
    strength: '💪',
    peace: '🕊️',
    growth: '🌱',
    fitness: '💪',
    career: '🏢',
    relationships: '❤️',
    confidence: '✨',
    mindfulness: '🧘',
    goals: '🎯',
  };

  // Category-specific AI prompts for premium features
  private static readonly CATEGORY_PROMPTS = {
    fitness: {
      prompt: "Generate an inspiring fitness and health motivation quote about strength, perseverance, wellness, or physical achievement. Focus on themes like: pushing through challenges, building strength, healthy habits, body positivity, endurance, or celebrating physical progress. Keep it motivational and empowering. Maximum 25 words.",
      icon: '💪',
      theme: 'fitness'
    },
    career: {
      prompt: "Create a motivational quote about professional success, career growth, and workplace ambition. Focus on themes like: leadership, professional development, achieving goals at work, overcoming workplace challenges, building skills, or career advancement. Keep it inspiring and professional. Maximum 25 words.",
      icon: '🏢',
      theme: 'career'
    },
    relationships: {
      prompt: "Generate a heartwarming quote about love, relationships, and human connections. Focus on themes like: building meaningful relationships, showing love and kindness, family bonds, friendship, romantic love, or emotional connection. Keep it warm and uplifting. Maximum 25 words.",
      icon: '❤️',
      theme: 'relationships'
    },
    confidence: {
      prompt: "Create an empowering quote about self-esteem, inner strength, and personal confidence. Focus on themes like: believing in yourself, overcoming self-doubt, embracing your worth, personal empowerment, self-love, or building confidence. Keep it uplifting and empowering. Maximum 25 words.",
      icon: '✨',
      theme: 'confidence'
    },
    mindfulness: {
      prompt: "Generate a peaceful quote about mindfulness, meditation, and living in the present moment. Focus on themes like: inner peace, mindful living, meditation, gratitude, being present, letting go of stress, or finding calm. Keep it serene and contemplative. Maximum 25 words.",
      icon: '🧘',
      theme: 'mindfulness'
    },
    goals: {
      prompt: "Create an ambitious quote about achieving dreams, setting goals, and personal success. Focus on themes like: pursuing dreams, goal achievement, ambition, persistence, overcoming obstacles, or reaching milestones. Keep it motivating and goal-oriented. Maximum 25 words.",
      icon: '🎯',
      theme: 'goals'
    },
    motivational: {
      prompt: "Generate a general motivational quote that inspires and uplifts. Focus on themes like: personal growth, positive mindset, overcoming challenges, daily inspiration, or general life motivation. Keep it universally inspiring and positive. Maximum 25 words.",
      icon: '⭐',
      theme: 'motivational'
    }
  };

  // Expanded collection of motivational message templates
  private static motivationalMessages = [
    "Today is a new chapter in your story. Make it an inspiring one! 📖✨",
    "Your potential is limitless. Believe in yourself and watch magic happen! 🌟💫",
    "Every small step forward is progress. Keep moving toward your dreams! 👣🎯",
    "You have the power to create positive change in your life today! ⚡💪",
    "Challenges are just opportunities wearing disguises. Embrace them! 🎭🚀",
    "Your mindset shapes your reality. Choose thoughts that empower you! 🧠💎",
    "Success is not final, failure is not fatal. What matters is the courage to continue! 🦁❤️",
    "You are stronger than your excuses and more powerful than your fears! 💪🔥",
    "Today's struggles are tomorrow's strengths. Keep pushing forward! 🏋️‍♀️🌈",
    "Your dreams don't have an expiration date. It's never too late to pursue them! ⏰🌟",
    "Be yourself unapologetically. The world needs your unique gifts! 🎁🌍",
    "Progress, not perfection, is the goal. Celebrate your journey! 🎉📈",
    "You are the architect of your own destiny. Build something beautiful! 🏗️🏛️",
    "Every expert was once a beginner. Your time to shine is coming! 🌅⭐",
    "Kindness is a strength, not a weakness. Spread it generously today! 💝🤲",
    "Your comeback story starts with your comeback attitude! 📚💯",
    "Believe in the magic of new beginnings. Today is yours! ✨🆕",
    "You don't have to be perfect to be amazing. You already are! 🌟😊",
    "Your journey is unique. Don't compare your chapter 1 to someone's chapter 20! 📖🔢",
    "The only impossible journey is the one you never begin! 🛤️🚶‍♀️",
    "You have survived 100% of your worst days. You're stronger than you think! 💪🌈",
    "Today is a gift. That's why it's called the present! 🎁⏰",
    "Your positive energy can light up someone's entire day! 💡🌞",
    "Every sunrise brings new opportunities. What will you create today? 🌅🎨",
    "You are not just existing, you are evolving. Embrace your growth! 🦋🌱",
  ];

  private static philosophicalMessages = [
    "Like a river shapes the landscape, your thoughts shape your reality! 🌊🏔️",
    "A seed doesn't doubt its ability to become a tree. Trust your growth! 🌱🌳",
    "Stars shine brightest in the darkest nights. Your light matters! ⭐🌙",
    "The mountain doesn't boast about its height. Be humble in your greatness! ⛰️🙏",
  ];

  static async getDailyMessage(): Promise<string> {
    const today = new Date().toDateString();
    const savedDate = await StorageService.getLastMessageDate();
    const savedMessage = await StorageService.getDailyMessage();

    // If we already have a message for today, return it
    if (savedDate === today && savedMessage) {
      return savedMessage;
    }

    // Generate a new message for today
    return this.generateNewMessage();
  }

  static async generateNewMessage(messageType: string = 'motivational'): Promise<string> {
    let messages: string[];

    switch (messageType) {
      case 'philosophical':
        messages = this.philosophicalMessages;
        break;
      case 'motivational':
      default:
        messages = this.motivationalMessages;
        break;
    }

    // Use date as seed for consistent daily messages
    const today = new Date();
    const dateString = today.toDateString();
    const seed = this.hashCode(dateString + messageType);
    const messageIndex = Math.abs(seed) % messages.length;

    const selectedMessage = messages[messageIndex];

    // Add some personalization based on time of day
    const personalizedMessage = this.personalizeMessage(selectedMessage, today);

    // Save the message for today
    await StorageService.setDailyMessage(personalizedMessage);
    await StorageService.setLastMessageDate(dateString);

    // Add to message history
    const historyItem: MessageHistoryItem = {
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      message: personalizedMessage,
      type: messageType,
      timestamp: Date.now(),
      isFavorite: false,
      icon: this.MESSAGE_ICONS[messageType as keyof typeof this.MESSAGE_ICONS] || '⭐',
    };

    await StorageService.addToHistory(historyItem);

    return personalizedMessage;
  }

  // Get category prompt for AI generation (Premium feature)
  static getCategoryPrompt(category: string): { prompt: string; icon: string; theme: string } | null {
    const categoryData = this.CATEGORY_PROMPTS[category as keyof typeof this.CATEGORY_PROMPTS];
    return categoryData || null;
  }

  // Get all available categories for UI
  static getAvailableCategories(): Array<{ key: string; name: string; icon: string; theme: string }> {
    return Object.entries(this.CATEGORY_PROMPTS).map(([key, data]) => ({
      key,
      name: this.formatCategoryName(key),
      icon: data.icon,
      theme: data.theme
    }));
  }

  // Format category key to display name
  private static formatCategoryName(key: string): string {
    const nameMap: { [key: string]: string } = {
      fitness: 'Fitness & Health',
      career: 'Career & Success',
      relationships: 'Relationships & Love',
      confidence: 'Self-Esteem & Confidence',
      mindfulness: 'Mindfulness & Peace',
      goals: 'Goals & Achievement',
      motivational: 'General Motivation'
    };
    return nameMap[key] || key.charAt(0).toUpperCase() + key.slice(1);
  }

  // Generate AI-powered message for specific category (Premium feature)
  static async generateAIMessage(category: string = 'motivational'): Promise<string> {
    const categoryData = this.getCategoryPrompt(category);

    if (!categoryData) {
      // Fallback to regular message generation
      return this.generateNewMessage(category);
    }

    // Validate the prompt before sending to AI
    if (!AIService.validatePrompt(categoryData.prompt)) {
      console.warn('Invalid AI prompt, falling back to regular messages');
      return this.generateNewMessage(category);
    }

    // Generate AI-powered message
    const aiResponse = await AIService.generateMessage(categoryData.prompt);
    
    let finalMessage: string;
    
    if (aiResponse.success && aiResponse.message) {
      // Use AI-generated message
      finalMessage = aiResponse.message;
    } else {
      // Fallback to regular message generation if AI fails
      console.warn('AI generation failed, falling back to regular messages:', aiResponse.error);
      return this.generateNewMessage(category);
    }

    // Add appropriate emoji if not already present
    if (!finalMessage.match(/[\u{1F300}-\u{1F9FF}]/u)) {
      finalMessage = `${finalMessage} ${categoryData.icon}`;
    }

    // Save the AI-generated message for today
    const today = new Date().toDateString();
    await StorageService.setDailyMessage(finalMessage);
    await StorageService.setLastMessageDate(today);

    // Add to message history
    const historyItem: MessageHistoryItem = {
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      message: finalMessage,
      type: category,
      timestamp: Date.now(),
      isFavorite: false,
      icon: categoryData.icon,
    };

    await StorageService.addToHistory(historyItem);

    return finalMessage;
  }

  private static personalizeMessage(message: string, date: Date): string {
    const hour = date.getHours();
    let greeting = '';
    
    if (hour < 6) {
      greeting = 'Early riser! ';
    } else if (hour < 12) {
      greeting = 'Good morning! ';
    } else if (hour < 17) {
      greeting = 'Good afternoon! ';
    } else if (hour < 22) {
      greeting = 'Good evening! ';
    } else {
      greeting = 'Night owl! ';
    }

    // Randomly decide whether to add greeting (50% chance)
    const shouldAddGreeting = Math.random() > 0.5;
    
    return shouldAddGreeting ? greeting + message : message;
  }

  // Simple hash function for consistent randomization
  private static hashCode(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash;
  }

  static async getRandomMessage(messageType: string = 'motivational'): Promise<string> {
    let messages: string[];
    
    switch (messageType) {
      case 'philosophical':
        messages = this.philosophicalMessages;
        break;
      case 'motivational':
      default:
        messages = this.motivationalMessages;
        break;
    }

    const randomIndex = Math.floor(Math.random() * messages.length);
    return messages[randomIndex];
  }

  // Get recent messages for the Recent Messages section
  static async getRecentMessages(limit: number = 3): Promise<MessageHistoryItem[]> {
    const history = await StorageService.getMessageHistory();
    return history.slice(0, limit);
  }

  // Format timestamp for display
  static formatTimestamp(timestamp: number): string {
    const now = Date.now();
    const diff = now - timestamp;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) {
      const hours = Math.floor(diff / (1000 * 60 * 60));
      if (hours === 0) {
        const minutes = Math.floor(diff / (1000 * 60));
        return minutes <= 1 ? 'Just now' : `${minutes} minutes ago`;
      }
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (days === 1) {
      return 'Yesterday, 8:00 AM';
    } else if (days === 2) {
      return '2 days ago, 8:00 AM';
    } else if (days === 3) {
      return '3 days ago, 8:00 AM';
    } else {
      const date = new Date(timestamp);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    }
  }

  // Toggle favorite status
  static async toggleFavorite(messageId: string): Promise<boolean> {
    return await StorageService.toggleFavorite(messageId);
  }
}