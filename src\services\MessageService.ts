import {StorageService, MessageHistoryItem} from './StorageService';
import {AIService} from './AIService';

export class MessageService {
  // Icon mapping for different message types
  private static readonly MESSAGE_ICONS = {
    motivational: '⭐',
    philosophical: '🔮',
    success: '🎯',
    happiness: '😊',
    wisdom: '🦉',
    strength: '💪',
    peace: '🕊️',
    growth: '🌱',
  };

  // Expanded collection of motivational message templates
  private static motivationalMessages = [
    "Today is a new chapter in your story. Make it an inspiring one! 📖✨",
    "Your potential is limitless. Believe in yourself and watch magic happen! 🌟💫",
    "Every small step forward is progress. Keep moving toward your dreams! 👣🎯",
    "You have the power to create positive change in your life today! ⚡💪",
    "Challenges are just opportunities wearing disguises. Embrace them! 🎭🚀",
    "Your mindset shapes your reality. Choose thoughts that empower you! 🧠💎",
    "Success is not final, failure is not fatal. What matters is the courage to continue! 🦁❤️",
    "You are stronger than your excuses and more powerful than your fears! 💪🔥",
    "Today's struggles are tomorrow's strengths. Keep pushing forward! 🏋️‍♀️🌈",
    "Your dreams don't have an expiration date. It's never too late to pursue them! ⏰🌟",
    "Be yourself unapologetically. The world needs your unique gifts! 🎁🌍",
    "Progress, not perfection, is the goal. Celebrate your journey! 🎉📈",
    "You are the architect of your own destiny. Build something beautiful! 🏗️🏛️",
    "Every expert was once a beginner. Your time to shine is coming! 🌅⭐",
    "Kindness is a strength, not a weakness. Spread it generously today! 💝🤲",
    "Your comeback story starts with your comeback attitude! 📚💯",
    "Believe in the magic of new beginnings. Today is yours! ✨🆕",
    "You don't have to be perfect to be amazing. You already are! 🌟😊",
    "Your journey is unique. Don't compare your chapter 1 to someone's chapter 20! 📖🔢",
    "The only impossible journey is the one you never begin! 🛤️🚶‍♀️",
    "You have survived 100% of your worst days. You're stronger than you think! 💪🌈",
    "Today is a gift. That's why it's called the present! 🎁⏰",
    "Your positive energy can light up someone's entire day! 💡🌞",
    "Every sunrise brings new opportunities. What will you create today? 🌅🎨",
    "You are not just existing, you are evolving. Embrace your growth! 🦋🌱",
  ];

  private static philosophicalMessages = [
    "Like a river shapes the landscape, your thoughts shape your reality! 🌊🏔️",
    "A seed doesn't doubt its ability to become a tree. Trust your growth! 🌱🌳",
    "Stars shine brightest in the darkest nights. Your light matters! ⭐🌙",
    "The mountain doesn't boast about its height. Be humble in your greatness! ⛰️🙏",
  ];

  static async getDailyMessage(): Promise<string> {
    const today = new Date().toDateString();
    const savedDate = await StorageService.getLastMessageDate();
    const savedMessage = await StorageService.getDailyMessage();

    // If we already have a message for today, return it
    if (savedDate === today && savedMessage) {
      return savedMessage;
    }

    // Generate a new message for today
    return this.generateNewMessage();
  }

  static async generateNewMessage(messageType: string = 'motivational'): Promise<string> {
    let messages: string[];
    
    switch (messageType) {
      case 'philosophical':
        messages = this.philosophicalMessages;
        break;
      case 'motivational':
      default:
        messages = this.motivationalMessages;
        break;
    }

    // Use date as seed for consistent daily messages
    const today = new Date();
    const dateString = today.toDateString();
    const seed = this.hashCode(dateString + messageType);
    const messageIndex = Math.abs(seed) % messages.length;
    
    const selectedMessage = messages[messageIndex];
    
    // Add some personalization based on time of day
    const personalizedMessage = this.personalizeMessage(selectedMessage, today);
    
    // Save the message for today
    await StorageService.setDailyMessage(personalizedMessage);
    await StorageService.setLastMessageDate(dateString);

    // Add to message history
    const historyItem: MessageHistoryItem = {
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      message: personalizedMessage,
      type: messageType,
      timestamp: Date.now(),
      isFavorite: false,
      icon: this.MESSAGE_ICONS[messageType as keyof typeof this.MESSAGE_ICONS] || '⭐',
    };

    await StorageService.addToHistory(historyItem);

    return personalizedMessage;
  }

  private static personalizeMessage(message: string, date: Date): string {
    const hour = date.getHours();
    let greeting = '';
    
    if (hour < 6) {
      greeting = 'Early riser! ';
    } else if (hour < 12) {
      greeting = 'Good morning! ';
    } else if (hour < 17) {
      greeting = 'Good afternoon! ';
    } else if (hour < 22) {
      greeting = 'Good evening! ';
    } else {
      greeting = 'Night owl! ';
    }

    // Randomly decide whether to add greeting (50% chance)
    const shouldAddGreeting = Math.random() > 0.5;
    
    return shouldAddGreeting ? greeting + message : message;
  }

  // Simple hash function for consistent randomization
  private static hashCode(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash;
  }

  static async getRandomMessage(messageType: string = 'motivational'): Promise<string> {
    let messages: string[];
    
    switch (messageType) {
      case 'philosophical':
        messages = this.philosophicalMessages;
        break;
      case 'motivational':
      default:
        messages = this.motivationalMessages;
        break;
    }

    const randomIndex = Math.floor(Math.random() * messages.length);
    return messages[randomIndex];
  }

  // Get recent messages for the Recent Messages section
  static async getRecentMessages(limit: number = 3): Promise<MessageHistoryItem[]> {
    const history = await StorageService.getMessageHistory();
    return history.slice(0, limit);
  }

  // Format timestamp for display
  static formatTimestamp(timestamp: number): string {
    const now = Date.now();
    const diff = now - timestamp;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) {
      const hours = Math.floor(diff / (1000 * 60 * 60));
      if (hours === 0) {
        const minutes = Math.floor(diff / (1000 * 60));
        return minutes <= 1 ? 'Just now' : `${minutes} minutes ago`;
      }
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (days === 1) {
      return 'Yesterday, 8:00 AM';
    } else if (days === 2) {
      return '2 days ago, 8:00 AM';
    } else if (days === 3) {
      return '3 days ago, 8:00 AM';
    } else {
      const date = new Date(timestamp);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    }
  }

  // Toggle favorite status
  static async toggleFavorite(messageId: string): Promise<boolean> {
    return await StorageService.toggleFavorite(messageId);
  }
}