package com.dailymessageapp

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.widget.RemoteViews

/**
 * Implementation of App Widget functionality.
 */
class WidgetProvider : AppWidgetProvider() {
    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        // There may be multiple widgets active, so update all of them
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onDeleted(context: Context, appWidgetIds: IntArray) {
        // When user removes widgets, clean up stored preferences
        for (appWidgetId in appWidgetIds) {
            WidgetConfigurationActivity.deleteWidgetPrefs(context, appWidgetId)
        }
    }

    override fun onEnabled(context: Context) {
        // Start widget update service when first widget is created
        WidgetUpdateService.scheduleUpdates(context, WidgetUpdateService.UPDATE_INTERVAL_1_HOUR)
    }

    override fun onDisabled(context: Context) {
        // Stop widget updates when last widget is removed
        // Could cancel scheduled updates here if needed
    }
}

internal fun updateAppWidget(
    context: Context,
    appWidgetManager: AppWidgetManager,
    appWidgetId: Int
) {
    // Get widget configuration
    val category = WidgetConfigurationActivity.loadCategoryPref(context, appWidgetId)
    val layoutType = WidgetConfigurationActivity.loadLayoutPref(context, appWidgetId)
    
    // Get today's stored message for this category
    val message = getDailyStoredMessage(context, category)
    
    // Select appropriate layout
    val layoutRes = when (layoutType) {
        "1x1" -> R.layout.widget_layout_1x1
        "2x1" -> R.layout.widget_layout_2x1
        "4x2" -> R.layout.widget_layout_4x2
        else -> R.layout.widget_layout
    }
    
    // Construct the RemoteViews object
    val views = RemoteViews(context.packageName, layoutRes)
    views.setTextViewText(R.id.appwidget_text, message)
    
    // Set up click to open main app (NO REFRESH - NO API CALLS)
    val openAppIntent = android.content.Intent(context, MainActivity::class.java)
    val openAppPendingIntent = android.app.PendingIntent.getActivity(
        context, appWidgetId, openAppIntent,
        android.app.PendingIntent.FLAG_UPDATE_CURRENT or android.app.PendingIntent.FLAG_IMMUTABLE
    )
    views.setOnClickPendingIntent(R.id.widget_container, openAppPendingIntent)

    // Instruct the widget manager to update the widget
    appWidgetManager.updateAppWidget(appWidgetId, views)
}

private fun getDailyStoredMessage(context: Context, category: String): String {
    // Get today's stored message from SharedPreferences (NO API CALL!)
    val today = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault()).format(java.util.Date())
    val prefs = context.getSharedPreferences("daily_messages", android.content.Context.MODE_PRIVATE)
    val messageKey = "${category}_$today"
    
    // Return stored message or fallback if not found
    return prefs.getString(messageKey, null) ?: getFallbackMessage(category)
}

private fun getFallbackMessage(category: String): String {
    // Static fallback messages (NO API COST)
    return when (category) {
        "fitness" -> "💪 Your strength grows with every challenge you face!"
        "career" -> "🏢 Success is built one determined step at a time!"
        "relationships" -> "❤️ Love and kindness create the strongest connections!"
        "confidence" -> "✨ You have everything within you to shine bright!"
        "mindfulness" -> "🧘 Find peace in this moment, right here, right now!"
        "goals" -> "🎯 Every dream begins with the courage to start!"
        else -> "🌟 Today is full of possibilities waiting for you!"
    }
}
