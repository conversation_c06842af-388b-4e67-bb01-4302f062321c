<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Outer shadow -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#0F172A"
                android:endColor="#1E293B"
                android:angle="135" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- Main card background -->
    <item
        android:top="2dp"
        android:left="2dp"
        android:right="2dp"
        android:bottom="2dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#4C1D95"
                android:endColor="#374151"
                android:angle="135" />
            <corners android:radius="18dp" />
            <stroke
                android:width="1dp"
                android:color="#4B5563" />
        </shape>
    </item>
    
    <!-- Inner highlight -->
    <item
        android:top="4dp"
        android:left="4dp"
        android:right="4dp"
        android:bottom="4dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#6B7280"
                android:centerColor="#00FFFFFF"
                android:endColor="#00FFFFFF"
                android:angle="135"
                android:type="radial"
                android:gradientRadius="100dp" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
</layer-list> 