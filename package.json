{"name": "daily-message-app", "version": "1.0.0", "description": "A beautiful daily motivational message app with AI-generated content", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "react-native-push-notification": "^8.1.1", "@react-native-async-storage/async-storage": "^1.19.3", "react-native-vector-icons": "^10.0.0", "react-native-linear-gradient": "^2.8.3", "@react-native-community/datetimepicker": "^7.6.2", "react-native-permissions": "^3.10.1", "react-native-device-info": "^10.11.0", "react-native-gesture-handler": "^2.13.1", "react-native-reanimated": "^3.5.4", "react-native-safe-area-context": "^4.7.4", "@react-navigation/native": "^6.1.8", "@react-navigation/bottom-tabs": "^6.5.9", "@react-navigation/native-stack": "^6.9.14"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}