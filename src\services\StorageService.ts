import AsyncStorage from '@react-native-async-storage/async-storage';

export interface MessageHistoryItem {
  id: string;
  message: string;
  type: string;
  timestamp: number;
  isFavorite: boolean;
  icon: string;
}

export class StorageService {
  private static readonly KEYS = {
    DAILY_MESSAGE: 'daily_message',
    LAST_MESSAGE_DATE: 'last_message_date',
    MESSAGE_TYPE: 'message_type',
    NOTIFICATIONS_ENABLED: 'notifications_enabled',
    NOTIFICATION_TIME: 'notification_time',
    USER_PREFERENCES: 'user_preferences',
    MESSAGE_HISTORY: 'message_history',
    FAVORITES: 'favorites',
  };

  // Daily Message Methods
  static async getDailyMessage(): Promise<string> {
    try {
      const message = await AsyncStorage.getItem(this.KEYS.DAILY_MESSAGE);
      return message || '';
    } catch (error) {
      console.error('Error getting daily message:', error);
      return '';
    }
  }

  static async setDailyMessage(message: string): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.DAILY_MESSAGE, message);
    } catch (error) {
      console.error('Error setting daily message:', error);
    }
  }

  static async getLastMessageDate(): Promise<string> {
    try {
      const date = await AsyncStorage.getItem(this.KEYS.LAST_MESSAGE_DATE);
      return date || '';
    } catch (error) {
      console.error('Error getting last message date:', error);
      return '';
    }
  }

  static async setLastMessageDate(date: string): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.LAST_MESSAGE_DATE, date);
    } catch (error) {
      console.error('Error setting last message date:', error);
    }
  }

  // Message Type Methods
  static async getMessageType(): Promise<string> {
    try {
      const type = await AsyncStorage.getItem(this.KEYS.MESSAGE_TYPE);
      return type || 'motivational'; // Default to motivational
    } catch (error) {
      console.error('Error getting message type:', error);
      return 'motivational';
    }
  }

  static async setMessageType(type: string): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.MESSAGE_TYPE, type);
    } catch (error) {
      console.error('Error setting message type:', error);
    }
  }

  // Notification Methods
  static async getNotificationsEnabled(): Promise<boolean> {
    try {
      const enabled = await AsyncStorage.getItem(this.KEYS.NOTIFICATIONS_ENABLED);
      return enabled === 'true'; // Default to true for new users
    } catch (error) {
      console.error('Error getting notifications enabled:', error);
      return true;
    }
  }

  static async setNotificationsEnabled(enabled: boolean): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.NOTIFICATIONS_ENABLED, enabled.toString());
    } catch (error) {
      console.error('Error setting notifications enabled:', error);
    }
  }

  static async getNotificationTime(): Promise<string> {
    try {
      const time = await AsyncStorage.getItem(this.KEYS.NOTIFICATION_TIME);
      // Default to 9:00 AM if no time is set
      return time || new Date(Date.now()).setHours(9, 0, 0, 0).toString();
    } catch (error) {
      console.error('Error getting notification time:', error);
      return new Date(Date.now()).setHours(9, 0, 0, 0).toString();
    }
  }

  static async setNotificationTime(time: string): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.NOTIFICATION_TIME, time);
    } catch (error) {
      console.error('Error setting notification time:', error);
    }
  }

  // User Preferences Methods
  static async getUserPreferences(): Promise<any> {
    try {
      const preferences = await AsyncStorage.getItem(this.KEYS.USER_PREFERENCES);
      return preferences ? JSON.parse(preferences) : {};
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return {};
    }
  }

  static async setUserPreferences(preferences: any): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.USER_PREFERENCES, JSON.stringify(preferences));
    } catch (error) {
      console.error('Error setting user preferences:', error);
    }
  }

  // Utility Methods
  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove(Object.values(this.KEYS));
      console.log('All app data cleared');
    } catch (error) {
      console.error('Error clearing all data:', error);
    }
  }

  static async getAllData(): Promise<any> {
    try {
      const keys = Object.values(this.KEYS);
      const values = await AsyncStorage.multiGet(keys);
      const data: any = {};
      
      values.forEach(([key, value]) => {
        data[key] = value;
      });
      
      return data;
    } catch (error) {
      console.error('Error getting all data:', error);
      return {};
    }
  }

  // Message History Methods
  static async getMessageHistory(): Promise<MessageHistoryItem[]> {
    try {
      const history = await AsyncStorage.getItem(this.KEYS.MESSAGE_HISTORY);
      return history ? JSON.parse(history) : [];
    } catch (error) {
      console.error('Error getting message history:', error);
      return [];
    }
  }

  static async addToHistory(message: MessageHistoryItem): Promise<void> {
    try {
      const history = await this.getMessageHistory();
      // Add new message to the beginning of the array
      history.unshift(message);
      // Keep only the last 50 messages
      const trimmedHistory = history.slice(0, 50);
      await AsyncStorage.setItem(this.KEYS.MESSAGE_HISTORY, JSON.stringify(trimmedHistory));
    } catch (error) {
      console.error('Error adding to message history:', error);
    }
  }

  static async updateMessageInHistory(messageId: string, updates: Partial<MessageHistoryItem>): Promise<void> {
    try {
      const history = await this.getMessageHistory();
      const updatedHistory = history.map(item =>
        item.id === messageId ? { ...item, ...updates } : item
      );
      await AsyncStorage.setItem(this.KEYS.MESSAGE_HISTORY, JSON.stringify(updatedHistory));
    } catch (error) {
      console.error('Error updating message in history:', error);
    }
  }

  // Favorites Methods
  static async getFavorites(): Promise<MessageHistoryItem[]> {
    try {
      const history = await this.getMessageHistory();
      return history.filter(item => item.isFavorite);
    } catch (error) {
      console.error('Error getting favorites:', error);
      return [];
    }
  }

  static async toggleFavorite(messageId: string): Promise<boolean> {
    try {
      const history = await this.getMessageHistory();
      const messageIndex = history.findIndex(item => item.id === messageId);

      if (messageIndex !== -1) {
        history[messageIndex].isFavorite = !history[messageIndex].isFavorite;
        await AsyncStorage.setItem(this.KEYS.MESSAGE_HISTORY, JSON.stringify(history));
        return history[messageIndex].isFavorite;
      }
      return false;
    } catch (error) {
      console.error('Error toggling favorite:', error);
      return false;
    }
  }

  // Initialize default settings for new users
  static async initializeDefaults(): Promise<void> {
    try {
      const isFirstLaunch = await AsyncStorage.getItem('first_launch');

      if (!isFirstLaunch) {
        // Set default values for new users
        await this.setMessageType('motivational');
        await this.setNotificationsEnabled(true);
        await this.setNotificationTime(new Date().setHours(9, 0, 0, 0).toString());

        // Add some sample messages for demo
        await this.addSampleMessages();

        await AsyncStorage.setItem('first_launch', 'false');
        console.log('Default settings initialized for new user');
      }
    } catch (error) {
      console.error('Error initializing defaults:', error);
    }
  }

  // Add sample messages for demo purposes
  private static async addSampleMessages(): Promise<void> {
    const now = Date.now();
    const sampleMessages: MessageHistoryItem[] = [
      {
        id: `${now - 86400000}-sample1`,
        message: "Success is not final, failure is not fatal: it is the courage to continue that counts.",
        type: 'motivational',
        timestamp: now - 86400000, // 1 day ago
        isFavorite: true,
        icon: '⭐',
      },
      {
        id: `${now - 172800000}-sample2`,
        message: "The only way to do great work is to love what you do.",
        type: 'motivational',
        timestamp: now - 172800000, // 2 days ago
        isFavorite: false,
        icon: '🔮',
      },
      {
        id: `${now - 259200000}-sample3`,
        message: "Believe you can and you're halfway there.",
        type: 'motivational',
        timestamp: now - 259200000, // 3 days ago
        isFavorite: true,
        icon: '💪',
      },
    ];

    for (const message of sampleMessages) {
      await this.addToHistory(message);
    }
  }
} 