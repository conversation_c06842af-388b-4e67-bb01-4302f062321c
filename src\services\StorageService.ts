import AsyncStorage from '@react-native-async-storage/async-storage';

export class StorageService {
  private static readonly KEYS = {
    DAILY_MESSAGE: 'daily_message',
    LAST_MESSAGE_DATE: 'last_message_date',
    MESSAGE_TYPE: 'message_type',
    NOTIFICATIONS_ENABLED: 'notifications_enabled',
    NOTIFICATION_TIME: 'notification_time',
    USER_PREFERENCES: 'user_preferences',
  };

  // Daily Message Methods
  static async getDailyMessage(): Promise<string> {
    try {
      const message = await AsyncStorage.getItem(this.KEYS.DAILY_MESSAGE);
      return message || '';
    } catch (error) {
      console.error('Error getting daily message:', error);
      return '';
    }
  }

  static async setDailyMessage(message: string): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.DAILY_MESSAGE, message);
    } catch (error) {
      console.error('Error setting daily message:', error);
    }
  }

  static async getLastMessageDate(): Promise<string> {
    try {
      const date = await AsyncStorage.getItem(this.KEYS.LAST_MESSAGE_DATE);
      return date || '';
    } catch (error) {
      console.error('Error getting last message date:', error);
      return '';
    }
  }

  static async setLastMessageDate(date: string): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.LAST_MESSAGE_DATE, date);
    } catch (error) {
      console.error('Error setting last message date:', error);
    }
  }

  // Message Type Methods
  static async getMessageType(): Promise<string> {
    try {
      const type = await AsyncStorage.getItem(this.KEYS.MESSAGE_TYPE);
      return type || 'motivational'; // Default to motivational
    } catch (error) {
      console.error('Error getting message type:', error);
      return 'motivational';
    }
  }

  static async setMessageType(type: string): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.MESSAGE_TYPE, type);
    } catch (error) {
      console.error('Error setting message type:', error);
    }
  }

  // Notification Methods
  static async getNotificationsEnabled(): Promise<boolean> {
    try {
      const enabled = await AsyncStorage.getItem(this.KEYS.NOTIFICATIONS_ENABLED);
      return enabled === 'true'; // Default to true for new users
    } catch (error) {
      console.error('Error getting notifications enabled:', error);
      return true;
    }
  }

  static async setNotificationsEnabled(enabled: boolean): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.NOTIFICATIONS_ENABLED, enabled.toString());
    } catch (error) {
      console.error('Error setting notifications enabled:', error);
    }
  }

  static async getNotificationTime(): Promise<string> {
    try {
      const time = await AsyncStorage.getItem(this.KEYS.NOTIFICATION_TIME);
      // Default to 9:00 AM if no time is set
      return time || new Date(Date.now()).setHours(9, 0, 0, 0).toString();
    } catch (error) {
      console.error('Error getting notification time:', error);
      return new Date(Date.now()).setHours(9, 0, 0, 0).toString();
    }
  }

  static async setNotificationTime(time: string): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.NOTIFICATION_TIME, time);
    } catch (error) {
      console.error('Error setting notification time:', error);
    }
  }

  // User Preferences Methods
  static async getUserPreferences(): Promise<any> {
    try {
      const preferences = await AsyncStorage.getItem(this.KEYS.USER_PREFERENCES);
      return preferences ? JSON.parse(preferences) : {};
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return {};
    }
  }

  static async setUserPreferences(preferences: any): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.USER_PREFERENCES, JSON.stringify(preferences));
    } catch (error) {
      console.error('Error setting user preferences:', error);
    }
  }

  // Utility Methods
  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove(Object.values(this.KEYS));
      console.log('All app data cleared');
    } catch (error) {
      console.error('Error clearing all data:', error);
    }
  }

  static async getAllData(): Promise<any> {
    try {
      const keys = Object.values(this.KEYS);
      const values = await AsyncStorage.multiGet(keys);
      const data: any = {};
      
      values.forEach(([key, value]) => {
        data[key] = value;
      });
      
      return data;
    } catch (error) {
      console.error('Error getting all data:', error);
      return {};
    }
  }

  // Initialize default settings for new users
  static async initializeDefaults(): Promise<void> {
    try {
      const isFirstLaunch = await AsyncStorage.getItem('first_launch');
      
      if (!isFirstLaunch) {
        // Set default values for new users
        await this.setMessageType('motivational');
        await this.setNotificationsEnabled(true);
        await this.setNotificationTime(new Date().setHours(9, 0, 0, 0).toString());
        
        await AsyncStorage.setItem('first_launch', 'false');
        console.log('Default settings initialized for new user');
      }
    } catch (error) {
      console.error('Error initializing defaults:', error);
    }
  }
} 