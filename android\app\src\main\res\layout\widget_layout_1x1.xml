<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:padding="@dimen/widget_margin">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:background="@drawable/widget_card_background"
        android:orientation="vertical"
        android:padding="12dp"
        android:gravity="center">

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginBottom="4dp"
            android:src="@drawable/auraflow_icon"
            android:contentDescription="AuraFlow Logo" />

        <TextView
            android:id="@+id/appwidget_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/appwidget_text"
            android:textColor="@color/widget_text_primary"
            android:textSize="10sp"
            android:textStyle="normal"
            android:lineSpacingMultiplier="1.2"
            android:gravity="center"
            android:maxLines="3"
            android:ellipsize="end"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="✨"
            android:textSize="8sp" />

    </LinearLayout>

</RelativeLayout>
