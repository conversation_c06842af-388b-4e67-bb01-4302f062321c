<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Daily Message App - Live Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #F8FAFC 0%, #E8D5FF 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .phone-container {
            max-width: 375px;
            width: 100%;
            background: #F8FAFC;
            border-radius: 30px;
            padding: 20px;
            box-shadow: 
                0 20px 40px rgba(0,0,0,0.1),
                inset 0 2px 16px rgba(255,255,255,0.6);
            min-height: 700px;
            position: relative;
        }

        .header {
            text-align: center;
            padding: 20px 0;
        }

        .greeting {
            font-size: 28px;
            font-weight: 700;
            color: #334155;
            margin-bottom: 8px;
        }

        .date {
            font-size: 16px;
            color: #64748B;
            font-weight: 500;
        }

        .clay-card {
            background: linear-gradient(135deg, #E8D5FF 0%, #FFFFFF 100%);
            border-radius: 20px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 
                0 8px 32px rgba(0,0,0,0.1),
                inset 0 2px 16px rgba(255,255,255,0.6);
            transition: all 0.3s ease;
        }

        .clay-card:hover {
            transform: translateY(-2px);
        }

        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .message-type {
            font-size: 18px;
            font-weight: 600;
            color: #334155;
            margin-left: 8px;
        }

        .daily-message {
            font-size: 20px;
            line-height: 1.6;
            color: #334155;
            font-weight: 500;
            text-align: center;
            transition: all 0.3s ease;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin: 20px 0;
        }

        .clay-button {
            flex: 1;
            background: linear-gradient(135deg, #C7F2D0 0%, #BFDBFE 100%);
            border: none;
            border-radius: 20px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            color: #334155;
            cursor: pointer;
            box-shadow: 
                0 8px 16px rgba(0,0,0,0.15),
                inset 0 2px 8px rgba(255,255,255,0.6);
            transition: all 0.2s ease;
        }

        .clay-button:hover {
            transform: translateY(-1px);
        }

        .clay-button:active {
            transform: scale(0.95);
        }

        .stats-card {
            background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFC 100%);
        }

        .stats-row {
            display: flex;
            justify-content: space-around;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #334155;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #64748B;
            font-weight: 500;
        }

        .demo-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            border-radius: 16px;
            padding: 20px;
            max-width: 300px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .demo-button {
            background: linear-gradient(135deg, #E8D5FF 0%, #C7F2D0 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 16px;
            margin: 8px 4px;
            cursor: pointer;
            font-weight: 600;
            color: #334155;
            transition: all 0.2s ease;
            display: block;
            width: 100%;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .notification-demo {
            position: fixed;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            background: #FFFFFF;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            max-width: 350px;
            display: none;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-20px); opacity: 0; }
            to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }

        .notification-demo.show {
            display: block;
        }

        .notification-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #E8D5FF 0%, #C7F2D0 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            float: left;
            margin-right: 16px;
            font-size: 24px;
        }

        @media (max-width: 768px) {
            .demo-info {
                position: static;
                margin-bottom: 20px;
                max-width: none;
            }
            
            body {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="demo-info">
        <h3 style="margin-bottom: 16px; color: #334155; text-align: center;">🎨 Daily Message App</h3>
        <p style="font-size: 14px; color: #64748B; margin-bottom: 16px; text-align: center;">
            Interactive preview of your beautiful claymorphism design!
        </p>
        <button class="demo-button" onclick="generateNewMessage()">✨ Generate New Message</button>
        <button class="demo-button" onclick="showNotificationDemo()">🔔 Show Notification</button>
        <button class="demo-button" onclick="showFeatures()">📱 View Features</button>
    </div>

    <div class="notification-demo" id="notificationDemo">
        <div class="notification-icon">✨</div>
        <div style="overflow: hidden;">
            <h4 style="font-size: 16px; font-weight: 600; color: #334155; margin-bottom: 4px;">
                Your Daily Message
            </h4>
            <p style="font-size: 14px; color: #64748B; line-height: 1.4;" id="notificationText">
                Your daily dose of inspiration is ready!
            </p>
        </div>
    </div>

    <div class="phone-container">
        <div class="header">
            <div class="greeting">Good day! 🌅</div>
            <div class="date" id="currentDate"></div>
        </div>

        <div class="clay-card">
            <div class="message-header">
                <span style="font-size: 24px;">✨</span>
                <div class="message-type">Motivational Message</div>
            </div>
            <div class="daily-message" id="dailyMessage">
                Today is a new chapter in your story. Make it an inspiring one! 📖✨
            </div>
        </div>

        <div class="action-buttons">
            <button class="clay-button" onclick="generateNewMessage()">
                ✨ New Message
            </button>
            <button class="clay-button" onclick="refreshAnimation()">
                🔄 Refresh
            </button>
        </div>

        <div class="clay-card stats-card">
            <div class="stats-row">
                <div class="stat-item">
                    <div class="stat-number">1</div>
                    <div class="stat-label">Today's Message</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">✨</div>
                    <div class="stat-label">AI Generated</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">🎯</div>
                    <div class="stat-label">Personalized</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const messages = [
            "Today is a new chapter in your story. Make it an inspiring one! 📖✨",
            "Your potential is limitless. Believe in yourself and watch magic happen! 🌟💫",
            "Every small step forward is progress. Keep moving toward your dreams! 👣🎯",
            "You have the power to create positive change in your life today! ⚡💪",
            "Challenges are just opportunities wearing disguises. Embrace them! 🎭🚀",
            "Your mindset shapes your reality. Choose thoughts that empower you! 🧠💎",
            "Success is not final, failure is not fatal. What matters is the courage to continue! 🦁❤️",
            "You are stronger than your excuses and more powerful than your fears! 💪🔥",
            "Today's struggles are tomorrow's strengths. Keep pushing forward! 🏋️‍♀️🌈",
            "Your dreams don't have an expiration date. It's never too late to pursue them! ⏰🌟",
            "Be yourself unapologetically. The world needs your unique gifts! 🎁🌍",
            "Progress, not perfection, is the goal. Celebrate your journey! 🎉📈",
            "You are the architect of your own destiny. Build something beautiful! 🏗️🏛️",
            "Every expert was once a beginner. Your time to shine is coming! 🌅⭐",
            "Kindness is a strength, not a weakness. Spread it generously today! 💝🤲"
        ];

        let currentMessageIndex = 0;

        function generateNewMessage() {
            const messageElement = document.getElementById('dailyMessage');
            currentMessageIndex = (currentMessageIndex + 1) % messages.length;
            
            messageElement.style.opacity = '0.3';
            messageElement.style.transform = 'translateY(10px)';
            
            setTimeout(() => {
                messageElement.textContent = messages[currentMessageIndex];
                messageElement.style.opacity = '1';
                messageElement.style.transform = 'translateY(0)';
            }, 300);
        }

        function refreshAnimation() {
            const messageElement = document.getElementById('dailyMessage');
            messageElement.style.transform = 'scale(0.95)';
            setTimeout(() => {
                messageElement.style.transform = 'scale(1)';
            }, 200);
        }

        function showNotificationDemo() {
            const notification = document.getElementById('notificationDemo');
            const notificationText = document.getElementById('notificationText');
            
            notificationText.textContent = messages[Math.floor(Math.random() * messages.length)];
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 4000);
        }

        function showFeatures() {
            alert(`🎨 Daily Message App Features:

✨ DESIGN HIGHLIGHTS:
• Claymorphism UI with soft, puffy elements
• 16-20px border radius throughout
• Soft inner & outer shadows for depth
• Beautiful pastel color palette
• Smooth animations & interactions

🤖 SMART FEATURES:
• AI-generated motivational messages
• Time-based personalization
• Daily consistent content
• Push notification system
• Persistent user settings

🔮 COMING SOON:
• Philosophical analogies
• Fitness motivation
• Mindfulness content
• Multi-language support

The real app includes a complete settings screen with notification controls, time picker, and much more!`);
        }

        // Set current date
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Interactive effects
        document.querySelectorAll('.clay-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });

        // Add random greeting based on time
        const hour = new Date().getHours();
        let greeting = 'Good day! 🌅';
        
        if (hour < 6) greeting = 'Early riser! 🌅';
        else if (hour < 12) greeting = 'Good morning! ☀️';
        else if (hour < 17) greeting = 'Good afternoon! 🌤️';
        else if (hour < 22) greeting = 'Good evening! 🌆';
        else greeting = 'Night owl! 🌙';
        
        document.querySelector('.greeting').textContent = greeting;
    </script>
</body>
</html> 