<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:padding="@dimen/widget_margin">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:background="@drawable/widget_card_background"
        android:orientation="horizontal"
        android:padding="14dp"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginRight="8dp"
            android:src="@drawable/auraflow_icon"
            android:contentDescription="AuraFlow Logo" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/appwidget_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/appwidget_text"
                android:textColor="@color/widget_text_primary"
                android:textSize="12sp"
                android:textStyle="normal"
                android:lineSpacingMultiplier="1.2"
                android:maxLines="2"
                android:ellipsize="end"
                android:fontFamily="sans-serif-medium" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="AuraFlow"
                android:textColor="@color/widget_text_secondary"
                android:textSize="8sp"
                android:fontFamily="sans-serif" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
