# 📱 Daily Message App

A beautiful React Native mobile app that delivers daily AI-generated motivational messages with stunning claymorphism design.

## ✨ Features

### Core Functionality
- 🤖 **AI-Generated Messages**: Unique, personalized motivational content daily
- 📅 **Daily Scheduling**: Set your preferred time for message delivery
- 🔔 **Push Notifications**: Reliable notification system for iOS and Android
- 🎨 **Message Categories**: Motivational messages with more types coming soon
- 💾 **Smart Storage**: Persistent settings and message history

### Design
- 🎨 **Claymorphism Design**: Soft, puffy UI elements with gentle shadows
- 🌈 **Pastel Color Palette**: Lavender, mint green, and baby blue themes
- 📱 **Responsive Layout**: Beautiful on all device sizes
- 🎭 **Interactive Elements**: Smooth animations and tactile feedback

## 🚀 Quick Start

### Prerequisites
- Node.js (>= 16)
- React Native CLI
- Android Studio (for Android development)
- Xcode (for iOS development - macOS only)

### Installation

1. **Clone and navigate to the project**
   ```bash
   cd "Daily Message App"
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **iOS Setup** (macOS only)
   ```bash
   cd ios && pod install && cd ..
   ```

4. **Run the app**
   ```bash
   # For Android
   npm run android

   # For iOS (macOS only)
   npm run ios
   ```

## 📋 Project Structure

```
Daily Message App/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── ClayButton.tsx   # Claymorphism button component
│   │   └── ClayCard.tsx     # Claymorphism card component
│   ├── screens/             # App screens
│   │   ├── HomeScreen.tsx   # Main daily message display
│   │   └── SettingsScreen.tsx # Settings and preferences
│   ├── services/            # Business logic
│   │   ├── MessageService.ts    # AI message generation
│   │   ├── NotificationService.ts # Push notifications
│   │   └── StorageService.ts    # Local data storage
│   └── theme/
│       └── colors.ts        # App color palette
├── App.tsx                  # Main app component
├── package.json            # Dependencies and scripts
└── README.md               # This file
```

## 🎯 Key Features Explained

### 🤖 AI Message Generation
- **Smart Daily Messages**: Consistent messages per day using date-based seeding
- **Time-Based Personalization**: Greetings adapt to morning, afternoon, or evening
- **Expandable Categories**: Architecture ready for philosophical, fitness, and mindfulness messages

### 🔔 Notification System
- **Cross-Platform**: Works on both iOS and Android
- **Reliable Scheduling**: Daily recurring notifications
- **Permission Handling**: Proper permission requests and fallbacks
- **Background Processing**: Messages generated even when app is closed

### 🎨 Claymorphism Design System
- **Soft Shadows**: Inner and outer shadows for depth
- **Rounded Corners**: 16-20px border radius throughout
- **Gentle Gradients**: Subtle color transitions
- **Interactive Feedback**: Scale animations on button press

### 💾 Data Persistence
- **Settings Storage**: Notification preferences and message types
- **Message History**: Daily message caching
- **User Preferences**: Customizable app behavior

## 🧪 Testing Scenarios

### Basic Functionality Tests
1. **App Launch**: Verify app opens and displays today's message
2. **Message Generation**: Test "New Message" button functionality
3. **Settings**: Confirm notification toggle and time picker work
4. **Persistence**: Close and reopen app to verify settings are saved

### Notification Tests
1. **Permission Request**: First-time notification permission flow
2. **Scheduling**: Set notification for 1 minute from now
3. **Background**: Minimize app and wait for notification
4. **Interaction**: Tap notification to open app

### Design Tests
1. **Responsiveness**: Test on different screen sizes
2. **Animation**: Verify button press animations
3. **Color Themes**: Check all card variants display correctly
4. **Accessibility**: Test with screen readers if needed

## 🔮 Future Roadmap

### Phase 1 (Current)
- ✅ Motivational messages
- ✅ Basic notification system
- ✅ Claymorphism UI

### Phase 2 (Coming Soon)
- 🧠 Philosophical analogies
- 🏃 Fitness motivation
- 🧘 Mindfulness messages
- 📊 Usage analytics

### Phase 3 (Future)
- 🌍 Multi-language support
- 🎵 Audio messages
- 🤝 Social sharing
- ☁️ Cloud sync

## 🛠️ Development

### Available Scripts
- `npm start` - Start Metro bundler
- `npm run android` - Run on Android
- `npm run ios` - Run on iOS (macOS only)
- `npm test` - Run tests
- `npm run lint` - Lint code

### Architecture Decisions
- **TypeScript**: Type safety and better developer experience
- **React Navigation**: Industry standard navigation
- **AsyncStorage**: Simple, reliable local storage
- **Modular Services**: Separated concerns for maintainability

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- Inspired by the need for daily motivation and beautiful mobile design
- Built with React Native for cross-platform compatibility
- Uses claymorphism design principles for a unique, tactile user experience

---

**Made with ❤️ for daily inspiration**
