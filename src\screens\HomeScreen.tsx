import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Animated,
  TouchableOpacity,
  Share,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';

import ClayCard from '../components/ClayCard';
import {colors} from '../theme/colors';
import {MessageService} from '../services/MessageService';
import {StorageService, MessageHistoryItem} from '../services/StorageService';

const HomeScreen: React.FC = () => {
  const [dailyMessage, setDailyMessage] = useState<string>('');
  const [messageType, setMessageType] = useState<string>('motivational');
  const [currentMessageId, setCurrentMessageId] = useState<string>('');
  const [isFavorite, setIsFavorite] = useState<boolean>(false);
  const [recentMessages, setRecentMessages] = useState<MessageHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    loadDailyMessage();
    loadRecentMessages();
  }, []);

  const loadDailyMessage = async () => {
    try {
      const message = await MessageService.getDailyMessage();
      const type = await StorageService.getMessageType();
      setDailyMessage(message);
      setMessageType(type);

      // Check if current message is in history and get its favorite status
      const history = await StorageService.getMessageHistory();
      const currentMsg = history.find(msg => msg.message === message);
      if (currentMsg) {
        setCurrentMessageId(currentMsg.id);
        setIsFavorite(currentMsg.isFavorite);
      }
    } catch (error) {
      console.error('Error loading daily message:', error);
      setDailyMessage('Stay positive and keep moving forward! 🌟');
    }
  };

  const loadRecentMessages = async () => {
    try {
      const recent = await MessageService.getRecentMessages(3);
      setRecentMessages(recent);
    } catch (error) {
      console.error('Error loading recent messages:', error);
    }
  };

  const generateNewMessage = async () => {
    setIsLoading(true);
    
    // Fade out animation
    Animated.timing(fadeAnim, {
      toValue: 0.3,
      duration: 300,
      useNativeDriver: true,
    }).start();

    try {
      const newMessage = await MessageService.generateNewMessage(messageType);
      setDailyMessage(newMessage);
      
      // Fade in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    } catch (error) {
      console.error('Error generating new message:', error);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setIsLoading(true);
    await loadDailyMessage();
    await loadRecentMessages();
    setIsLoading(false);
  };

  const shareMessage = async () => {
    try {
      await Share.share({
        message: `${dailyMessage}\n\n- AuraFlow`,
        title: 'Daily Motivation',
      });
    } catch (error) {
      console.error('Error sharing message:', error);
    }
  };

  const toggleFavorite = async () => {
    if (currentMessageId) {
      try {
        const newFavoriteStatus = await MessageService.toggleFavorite(currentMessageId);
        setIsFavorite(newFavoriteStatus);
      } catch (error) {
        console.error('Error toggling favorite:', error);
      }
    }
  };

  const getCurrentDate = () => {
    const today = new Date();
    return today.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={onRefresh} />
        }>
        <View style={styles.header}>
          <Text style={styles.greeting}>Good day! 🌅</Text>
          <Text style={styles.date}>{getCurrentDate()}</Text>
        </View>

        <ClayCard style={styles.messageCard} variant="primary">
          <View style={styles.messageHeader}>
            <Icon name="auto-awesome" size={24} color={colors.textPrimary} />
            <Text style={styles.messageType}>
              {messageType.charAt(0).toUpperCase() + messageType.slice(1)} Message
            </Text>
          </View>

          <Animated.View style={[{opacity: fadeAnim}]}>
            <Text style={styles.dailyMessage}>{dailyMessage}</Text>
          </Animated.View>

          <View style={styles.messageActions}>
            <View style={styles.messageInfo}>
              <Text style={styles.messageLabel}>Today's Motivation</Text>
              <Text style={styles.messageTime}>Generated at 8:00 AM</Text>
            </View>
            <View style={styles.actionIcons}>
              <TouchableOpacity
                style={styles.actionIcon}
                onPress={toggleFavorite}>
                <Icon
                  name={isFavorite ? 'favorite' : 'favorite-border'}
                  size={24}
                  color={isFavorite ? colors.accent : colors.textSecondary}
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.actionIcon}
                onPress={shareMessage}>
                <Icon
                  name="share"
                  size={24}
                  color={colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
          </View>
        </ClayCard>

        <View style={styles.recentSection}>
          <View style={styles.recentHeader}>
            <Text style={styles.recentTitle}>Recent Messages</Text>
            <TouchableOpacity>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          {recentMessages.map((message, index) => (
            <ClayCard key={message.id} style={styles.recentCard} variant="neutral">
              <View style={styles.recentMessageRow}>
                <View style={styles.recentIconContainer}>
                  <Text style={styles.recentIcon}>{message.icon}</Text>
                </View>
                <View style={styles.recentMessageInfo}>
                  <Text style={styles.recentMessageText} numberOfLines={2}>
                    {message.message}
                  </Text>
                  <Text style={styles.recentMessageTime}>
                    {MessageService.formatTimestamp(message.timestamp)}
                  </Text>
                </View>
                <TouchableOpacity
                  style={styles.recentFavoriteButton}
                  onPress={() => MessageService.toggleFavorite(message.id)}>
                  <Icon
                    name={message.isFavorite ? 'favorite' : 'favorite-border'}
                    size={20}
                    color={message.isFavorite ? colors.accent : colors.textSecondary}
                  />
                </TouchableOpacity>
              </View>
            </ClayCard>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  greeting: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  date: {
    fontSize: 16,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  messageCard: {
    margin: 20,
    marginBottom: 16,
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  messageType: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginLeft: 8,
  },
  dailyMessage: {
    fontSize: 20,
    lineHeight: 32,
    color: colors.textPrimary,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 20,
  },
  messageActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: colors.background,
  },
  messageInfo: {
    flex: 1,
  },
  messageLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 2,
  },
  messageTime: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '400',
  },
  actionIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  recentSection: {
    paddingHorizontal: 20,
  },
  recentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  recentTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
  },
  recentCard: {
    marginBottom: 12,
  },
  recentMessageRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  recentIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  recentIcon: {
    fontSize: 16,
  },
  recentMessageInfo: {
    flex: 1,
    marginRight: 12,
  },
  recentMessageText: {
    fontSize: 14,
    lineHeight: 20,
    color: colors.textPrimary,
    fontWeight: '500',
    marginBottom: 2,
  },
  recentMessageTime: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '400',
  },
  recentFavoriteButton: {
    padding: 4,
  },
});

export default HomeScreen; 