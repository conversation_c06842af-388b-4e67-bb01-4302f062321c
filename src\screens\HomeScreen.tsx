import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Animated,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';

import ClayCard from '../components/ClayCard';
import ClayButton from '../components/ClayButton';
import {colors} from '../theme/colors';
import {MessageService} from '../services/MessageService';
import {StorageService} from '../services/StorageService';

const HomeScreen: React.FC = () => {
  const [dailyMessage, setDailyMessage] = useState<string>('');
  const [messageType, setMessageType] = useState<string>('motivational');
  const [isLoading, setIsLoading] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    loadDailyMessage();
  }, []);

  const loadDailyMessage = async () => {
    try {
      const message = await MessageService.getDailyMessage();
      const type = await StorageService.getMessageType();
      setDailyMessage(message);
      setMessageType(type);
    } catch (error) {
      console.error('Error loading daily message:', error);
      setDailyMessage('Stay positive and keep moving forward! 🌟');
    }
  };

  const generateNewMessage = async () => {
    setIsLoading(true);
    
    // Fade out animation
    Animated.timing(fadeAnim, {
      toValue: 0.3,
      duration: 300,
      useNativeDriver: true,
    }).start();

    try {
      const newMessage = await MessageService.generateNewMessage(messageType);
      setDailyMessage(newMessage);
      
      // Fade in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    } catch (error) {
      console.error('Error generating new message:', error);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setIsLoading(true);
    await loadDailyMessage();
    setIsLoading(false);
  };

  const getCurrentDate = () => {
    const today = new Date();
    return today.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={onRefresh} />
        }>
        <View style={styles.header}>
          <Text style={styles.greeting}>Good day! 🌅</Text>
          <Text style={styles.date}>{getCurrentDate()}</Text>
        </View>

        <ClayCard style={styles.messageCard} variant="primary">
          <View style={styles.messageHeader}>
            <Icon name="auto-awesome" size={24} color={colors.textPrimary} />
            <Text style={styles.messageType}>
              {messageType.charAt(0).toUpperCase() + messageType.slice(1)} Message
            </Text>
          </View>
          
          <Animated.View style={[{opacity: fadeAnim}]}>
            <Text style={styles.dailyMessage}>{dailyMessage}</Text>
          </Animated.View>
        </ClayCard>

        <View style={styles.actionButtons}>
          <ClayButton
            title="✨ New Message"
            onPress={generateNewMessage}
            disabled={isLoading}
            variant="secondary"
            style={styles.button}
          />
          
          <ClayButton
            title="🔄 Refresh"
            onPress={onRefresh}
            disabled={isLoading}
            variant="accent"
            style={styles.button}
          />
        </View>

        <ClayCard style={styles.statsCard} variant="neutral">
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>1</Text>
              <Text style={styles.statLabel}>Today's Message</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>✨</Text>
              <Text style={styles.statLabel}>AI Generated</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>🎯</Text>
              <Text style={styles.statLabel}>Personalized</Text>
            </View>
          </View>
        </ClayCard>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  greeting: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  date: {
    fontSize: 16,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  messageCard: {
    margin: 20,
    marginBottom: 16,
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  messageType: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginLeft: 8,
  },
  dailyMessage: {
    fontSize: 20,
    lineHeight: 32,
    color: colors.textPrimary,
    fontWeight: '500',
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  button: {
    flex: 1,
    marginHorizontal: 8,
  },
  statsCard: {
    margin: 20,
    marginTop: 0,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
  },
});

export default HomeScreen; 