import PushNotification from 'react-native-push-notification';
import {Platform, PermissionsAndroid} from 'react-native';
import {MessageService} from './MessageService';

export class NotificationService {
  static initialize() {
    PushNotification.configure({
      onRegister: function (token) {
        console.log('TOKEN:', token);
      },
      onNotification: function (notification) {
        console.log('NOTIFICATION:', notification);
      },
      onAction: function (notification) {
        console.log('ACTION:', notification.action);
        console.log('NOTIFICATION:', notification);
      },
      onRegistrationError: function (err) {
        console.error(err.message, err);
      },
      permissions: {
        alert: true,
        badge: true,
        sound: true,
      },
      popInitialNotification: true,
      requestPermissions: Platform.OS === 'ios',
    });

    PushNotification.createChannel(
      {
        channelId: 'daily-message-channel',
        channelName: 'Daily Message Notifications',
        channelDescription: 'Notifications for daily inspirational messages',
        playSound: true,
        soundName: 'default',
        importance: 4,
        vibrate: true,
      },
      (created) => console.log(`createChannel returned '${created}'`)
    );

    if (Platform.OS === 'android') {
      this.requestAndroidPermissions();
    }
  }

  static async requestAndroidPermissions() {
    try {
      if (Platform.OS === 'android' && Platform.Version >= 33) {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
          {
            title: 'Notification Permission',
            message: 'Daily Message App needs permission to send you daily inspirational messages.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      }
      return true;
    } catch (err) {
      console.warn(err);
      return false;
    }
  }

  static async scheduleNotification(time: Date, messageType: string) {
    // Cancel existing notifications
    await this.cancelNotifications();

    // Generate a daily message for the notification
    const message = await MessageService.generateNewMessage(messageType);

    // Calculate the next notification time
    const now = new Date();
    const notificationTime = new Date();
    notificationTime.setHours(time.getHours());
    notificationTime.setMinutes(time.getMinutes());
    notificationTime.setSeconds(0);
    notificationTime.setMilliseconds(0);

    // If the time has already passed today, schedule for tomorrow
    if (notificationTime <= now) {
      notificationTime.setDate(notificationTime.getDate() + 1);
    }

    PushNotification.localNotificationSchedule({
      id: 'daily-message',
      channelId: 'daily-message-channel',
      title: '✨ Your Daily Message',
      message: message,
      date: notificationTime,
      repeatType: 'day',
      allowWhileIdle: true,
      playSound: true,
      soundName: 'default',
      vibrate: true,
      vibration: 300,
      actions: ['Open App'],
      invokeApp: true,
      smallIcon: 'ic_notification',
      largeIcon: 'ic_launcher',
    });

    console.log(`Notification scheduled for: ${notificationTime.toLocaleString()}`);
  }

  static async cancelNotifications() {
    PushNotification.cancelLocalNotification('daily-message');
    PushNotification.cancelAllLocalNotifications();
  }

  static async sendTestNotification(messageType: string) {
    const message = await MessageService.generateNewMessage(messageType);
    
    PushNotification.localNotification({
      id: 'test-message',
      channelId: 'daily-message-channel',
      title: '🧪 Test Message',
      message: message,
      playSound: true,
      soundName: 'default',
      vibrate: true,
      actions: ['Open App'],
      invokeApp: true,
    });
  }

  static checkPermissions() {
    PushNotification.checkPermissions((permissions) => {
      console.log('Notification permissions:', permissions);
      return permissions;
    });
  }
} 