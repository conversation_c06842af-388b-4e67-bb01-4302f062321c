import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';

import ClayCard from '../components/ClayCard';
import {colors} from '../theme/colors';
import {MessageService} from '../services/MessageService';
import {StorageService, MessageHistoryItem} from '../services/StorageService';

const HistoryScreen: React.FC = () => {
  const [messages, setMessages] = useState<MessageHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadMessages();
  }, []);

  const loadMessages = async () => {
    try {
      const history = await StorageService.getMessageHistory();
      setMessages(history);
    } catch (error) {
      console.error('Error loading message history:', error);
    }
  };

  const onRefresh = async () => {
    setIsLoading(true);
    await loadMessages();
    setIsLoading(false);
  };

  const toggleFavorite = async (messageId: string) => {
    try {
      const newFavoriteStatus = await MessageService.toggleFavorite(messageId);
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === messageId ? {...msg, isFavorite: newFavoriteStatus} : msg
        )
      );
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  const renderMessageItem = (item: MessageHistoryItem) => (
    <ClayCard key={item.id} style={styles.messageCard} variant="neutral">
      <View style={styles.messageHeader}>
        <View style={styles.iconContainer}>
          <Text style={styles.messageIcon}>{item.icon}</Text>
        </View>
        <View style={styles.messageInfo}>
          <Text style={styles.messageText}>{item.message}</Text>
          <Text style={styles.messageTime}>
            {MessageService.formatTimestamp(item.timestamp)}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={() => toggleFavorite(item.id)}>
          <Icon
            name={item.isFavorite ? 'favorite' : 'favorite-border'}
            size={24}
            color={item.isFavorite ? colors.accent : colors.textSecondary}
          />
        </TouchableOpacity>
      </View>
    </ClayCard>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>History</Text>
        <Text style={styles.subtitle}>Your message journey</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={onRefresh} />
        }>
        {messages.length > 0 ? (
          messages.map(renderMessageItem)
        ) : (
          <ClayCard style={styles.emptyCard} variant="neutral">
            <View style={styles.emptyState}>
              <Icon name="history" size={48} color={colors.textSecondary} />
              <Text style={styles.emptyTitle}>No Messages Yet</Text>
              <Text style={styles.emptySubtitle}>
                Your message history will appear here as you generate new messages.
              </Text>
            </View>
          </ClayCard>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  messageCard: {
    margin: 16,
    marginBottom: 8,
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  messageIcon: {
    fontSize: 20,
  },
  messageInfo: {
    flex: 1,
    marginRight: 12,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 24,
    color: colors.textPrimary,
    fontWeight: '500',
    marginBottom: 4,
  },
  messageTime: {
    fontSize: 14,
    color: colors.textSecondary,
    fontWeight: '400',
  },
  favoriteButton: {
    padding: 4,
  },
  emptyCard: {
    margin: 20,
    marginTop: 40,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default HistoryScreen;
